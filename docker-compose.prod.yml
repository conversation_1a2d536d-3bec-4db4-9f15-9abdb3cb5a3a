version: '3.8'

services:
  frontend:
    build:
      context: ./iotvision-frontend
      dockerfile: Dockerfile
      target: production
    container_name: iotvision-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    restart: always
    networks:
      - iotvision-prod-network
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend-prod.rule=Host(`iotvision.com`) || Host(`www.iotvision.com`)"
      - "traefik.http.routers.frontend-prod.entrypoints=websecure"
      - "traefik.http.routers.frontend-prod.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-prod.loadbalancer.server.port=80"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.routers.frontend-prod-http.rule=Host(`iotvision.com`) || Host(`www.iotvision.com`)"
      - "traefik.http.routers.frontend-prod-http.entrypoints=web"
      - "traefik.http.routers.frontend-prod-http.middlewares=redirect-to-https"

  traefik:
    image: traefik:v2.10
    container_name: iotvision-traefik-prod
    command:
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--log.level=INFO"
      - "--accesslog=true"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - letsencrypt:/letsencrypt
    networks:
      - iotvision-prod-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # Monitoring (optional)
  watchtower:
    image: containrrr/watchtower
    container_name: iotvision-watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600
    restart: always
    profiles:
      - monitoring

networks:
  iotvision-prod-network:
    driver: bridge
    name: iotvision-prod-network

volumes:
  letsencrypt:
    name: iotvision-letsencrypt
