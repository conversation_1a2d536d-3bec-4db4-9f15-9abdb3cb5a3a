#!/bin/bash

# IoTVision Deployment Script
# Usage: ./scripts/deploy.sh [environment]
# Environments: dev, staging, production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-dev}

echo -e "${BLUE}🚀 IoTVision Deployment Script${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed. Please install it and try again."
    exit 1
fi

print_status "docker-compose is available"

# Navigate to project root
cd "$(dirname "$0")/.."

case $ENVIRONMENT in
    "dev")
        print_status "Deploying to development environment..."
        
        # Stop existing containers
        docker-compose down --remove-orphans
        
        # Build and start containers
        docker-compose up --build -d
        
        print_status "Development environment deployed successfully!"
        echo -e "${BLUE}Frontend available at: http://localhost${NC}"
        ;;
        
    "staging")
        print_status "Deploying to staging environment..."
        
        # Stop existing containers
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml down --remove-orphans
        
        # Build and start containers
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
        
        print_status "Staging environment deployed successfully!"
        echo -e "${BLUE}Frontend available at: http://localhost${NC}"
        ;;
        
    "production")
        print_warning "Deploying to PRODUCTION environment..."
        read -p "Are you sure you want to deploy to production? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # Stop existing containers
            docker-compose -f docker-compose.prod.yml down --remove-orphans
            
            # Build and start containers
            docker-compose -f docker-compose.prod.yml up --build -d
            
            print_status "Production environment deployed successfully!"
            echo -e "${BLUE}Frontend available at: https://iotvision.com${NC}"
        else
            print_warning "Production deployment cancelled."
            exit 0
        fi
        ;;
        
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        echo "Valid environments: dev, staging, production"
        exit 1
        ;;
esac

# Wait for services to be healthy
echo ""
print_status "Waiting for services to be healthy..."
sleep 10

# Check service health
if docker-compose ps | grep -q "Up (healthy)"; then
    print_status "All services are healthy!"
else
    print_warning "Some services may not be fully ready yet. Check with: docker-compose ps"
fi

echo ""
print_status "Deployment completed successfully! 🎉"

# Show running containers
echo ""
echo -e "${BLUE}Running containers:${NC}"
docker-compose ps
