#!/bin/bash

# IoTVision Build Script
# Usage: ./scripts/build.sh [target]
# Targets: dev, prod

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default target
TARGET=${1:-dev}

echo -e "${BLUE}🔨 IoTVision Build Script${NC}"
echo -e "${BLUE}Target: ${TARGET}${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Navigate to project root
cd "$(dirname "$0")/.."

case $TARGET in
    "dev")
        print_status "Building development version..."
        
        # Navigate to frontend directory
        cd iotvision-frontend
        
        # Install dependencies
        print_status "Installing dependencies..."
        npm ci
        
        # Run development build
        print_status "Building application..."
        npm run build
        
        print_status "Development build completed!"
        ;;
        
    "prod")
        print_status "Building production version..."
        
        # Build Docker image
        print_status "Building Docker image..."
        docker build -t iotvision-frontend:latest ./iotvision-frontend
        
        print_status "Production build completed!"
        print_status "Docker image: iotvision-frontend:latest"
        ;;
        
    *)
        print_error "Invalid target: $TARGET"
        echo "Valid targets: dev, prod"
        exit 1
        ;;
esac

echo ""
print_status "Build completed successfully! 🎉"
