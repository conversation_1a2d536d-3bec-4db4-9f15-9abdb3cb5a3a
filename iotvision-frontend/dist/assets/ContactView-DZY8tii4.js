import{d as v,g as m,j as u,c as l,a as r,b as t,k as f,l as o,v as i,m as h,i as b,o as c,_ as g}from"./index-e7Y3w3wC.js";const y={class:"contact-view"},k={class:"contact-content section"},w={class:"container"},S={class:"contact-grid"},V={class:"contact-form-section"},j={class:"form-group"},x={class:"form-group"},M={class:"form-group"},C={class:"form-group"},_={class:"form-group"},q={class:"form-group"},T=["disabled"],E={key:0},P={key:1},U={key:2,class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},I=v({__name:"ContactView",setup(N){const d=m(!1),e=u({name:"",email:"",company:"",phone:"",subject:"",message:""}),p=async()=>{d.value=!0;try{await new Promise(n=>setTimeout(n,2e3)),Object.keys(e).forEach(n=>{e[n]=""}),alert("Message sent successfully! We'll get back to you soon.")}catch{alert("Error sending message. Please try again.")}finally{d.value=!1}};return(n,a)=>(c(),l("div",y,[a[16]||(a[16]=r('<section class="contact-hero section" data-v-59afdc01><div class="container" data-v-59afdc01><div class="hero-content" data-v-59afdc01><h1 class="hero-title" data-v-59afdc01> Get in <span class="text-gradient" data-v-59afdc01>Touch</span></h1><p class="hero-subtitle" data-v-59afdc01> Ready to transform your business with IoT? Let&#39;s start the conversation. </p></div></div></section>',1)),t("section",k,[t("div",w,[t("div",S,[t("div",V,[a[14]||(a[14]=t("h2",null,"Send us a Message",-1)),t("form",{onSubmit:f(p,["prevent"]),class:"contact-form"},[t("div",j,[a[6]||(a[6]=t("label",{for:"name"},"Full Name *",-1)),o(t("input",{type:"text",id:"name","onUpdate:modelValue":a[0]||(a[0]=s=>e.name=s),required:"",class:"form-input",placeholder:"Your full name"},null,512),[[i,e.name]])]),t("div",x,[a[7]||(a[7]=t("label",{for:"email"},"Email Address *",-1)),o(t("input",{type:"email",id:"email","onUpdate:modelValue":a[1]||(a[1]=s=>e.email=s),required:"",class:"form-input",placeholder:"<EMAIL>"},null,512),[[i,e.email]])]),t("div",M,[a[8]||(a[8]=t("label",{for:"company"},"Company",-1)),o(t("input",{type:"text",id:"company","onUpdate:modelValue":a[2]||(a[2]=s=>e.company=s),class:"form-input",placeholder:"Your company name"},null,512),[[i,e.company]])]),t("div",C,[a[9]||(a[9]=t("label",{for:"phone"},"Phone Number",-1)),o(t("input",{type:"tel",id:"phone","onUpdate:modelValue":a[3]||(a[3]=s=>e.phone=s),class:"form-input",placeholder:"+*********** 789"},null,512),[[i,e.phone]])]),t("div",_,[a[11]||(a[11]=t("label",{for:"subject"},"Subject *",-1)),o(t("select",{id:"subject","onUpdate:modelValue":a[4]||(a[4]=s=>e.subject=s),required:"",class:"form-select"},a[10]||(a[10]=[r('<option value="" data-v-59afdc01>Select a topic</option><option value="general" data-v-59afdc01>General Inquiry</option><option value="demo" data-v-59afdc01>Request Demo</option><option value="partnership" data-v-59afdc01>Partnership</option><option value="support" data-v-59afdc01>Technical Support</option><option value="pricing" data-v-59afdc01>Pricing Information</option>',6)]),512),[[h,e.subject]])]),t("div",q,[a[12]||(a[12]=t("label",{for:"message"},"Message *",-1)),o(t("textarea",{id:"message","onUpdate:modelValue":a[5]||(a[5]=s=>e.message=s),required:"",class:"form-textarea",rows:"5",placeholder:"Tell us about your project or requirements..."},null,512),[[i,e.message]])]),t("button",{type:"submit",class:"btn btn-primary form-submit",disabled:d.value},[d.value?(c(),l("span",P,"Sending...")):(c(),l("span",E,"Send Message")),d.value?b("",!0):(c(),l("svg",U,a[13]||(a[13]=[t("path",{d:"M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,T)],32)]),a[15]||(a[15]=r('<div class="contact-info-section" data-v-59afdc01><h2 data-v-59afdc01>Contact Information</h2><div class="contact-methods" data-v-59afdc01><div class="contact-method" data-v-59afdc01><div class="method-icon" data-v-59afdc01>📧</div><div class="method-content" data-v-59afdc01><h3 data-v-59afdc01>Email</h3><p data-v-59afdc01><EMAIL></p><p data-v-59afdc01><EMAIL></p></div></div><div class="contact-method" data-v-59afdc01><div class="method-icon" data-v-59afdc01>📞</div><div class="method-content" data-v-59afdc01><h3 data-v-59afdc01>Phone</h3><p data-v-59afdc01>+*********** 789</p><p data-v-59afdc01>Mon-Fri 9:00-18:00 CET</p></div></div><div class="contact-method" data-v-59afdc01><div class="method-icon" data-v-59afdc01>📍</div><div class="method-content" data-v-59afdc01><h3 data-v-59afdc01>Address</h3><p data-v-59afdc01>Wenceslas Square 1</p><p data-v-59afdc01>110 00 Prague, Czech Republic</p></div></div></div><div class="response-time" data-v-59afdc01><h3 data-v-59afdc01>Response Time</h3><div class="response-stats" data-v-59afdc01><div class="response-stat" data-v-59afdc01><span class="stat-value" data-v-59afdc01>&lt; 2h</span><span class="stat-label" data-v-59afdc01>Average Response</span></div><div class="response-stat" data-v-59afdc01><span class="stat-value" data-v-59afdc01>24/7</span><span class="stat-label" data-v-59afdc01>Emergency Support</span></div></div></div></div>',1))])])])]))}}),B=g(I,[["__scopeId","data-v-59afdc01"]]);export{B as default};
