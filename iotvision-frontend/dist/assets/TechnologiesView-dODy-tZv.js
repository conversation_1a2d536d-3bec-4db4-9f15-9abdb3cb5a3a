import{d as p,g as v,h,c as s,a as m,b as e,i as _,F as l,r,t as n,n as f,o as a,_ as g}from"./index-e7Y3w3wC.js";const y={class:"technologies-view"},b={class:"tech-details section"},A={class:"container"},k={class:"tech-tabs"},w=["onClick"],C={key:0,class:"tech-content"},S={class:"tech-overview"},E={class:"tech-info"},L={class:"tech-title"},M={class:"tech-description"},R={class:"tech-benefits"},T={class:"tech-visual"},B={class:"visual-placeholder"},I={class:"tech-icon"},N={class:"tech-stats"},x={class:"stat-value"},O={class:"stat-label"},P={class:"tech-features"},V={class:"features-grid"},F={class:"feature-specs"},D=p({__name:"TechnologiesView",setup(z){const d=v(0),u=[{id:1,name:"Connectivity",icon:"📡",description:"Advanced multi-protocol connectivity solutions ensuring seamless communication between devices across various networks and environments.",benefits:["Multi-protocol support (WiFi 6, 5G, LoRaWAN, Bluetooth 5.0)","Extended range up to 10km","Ultra-low latency (<1ms)","Automatic failover and redundancy"],stats:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km"},{label:"Latency",value:"<1ms"}],features:[{name:"Mesh Networking",description:"Self-healing network topology that automatically routes around failures",specs:["Auto-discovery","Load balancing","Redundancy"]},{name:"Edge Computing",description:"Process data locally to reduce latency and bandwidth usage",specs:["Real-time processing","Local storage","Offline capability"]}]},{id:2,name:"AI & Analytics",icon:"🧠",description:"Intelligent data processing with machine learning algorithms that transform raw sensor data into actionable business insights.",benefits:["Real-time anomaly detection","Predictive maintenance capabilities","Automated decision making","99.5% accuracy in pattern recognition"],stats:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:[{name:"Machine Learning",description:"Advanced ML models for pattern recognition and prediction",specs:["Deep learning","Neural networks","Auto-training"]},{name:"Data Analytics",description:"Comprehensive analytics platform for business intelligence",specs:["Real-time dashboards","Custom reports","API access"]}]},{id:3,name:"Security",icon:"🔒",description:"Enterprise-grade security framework with end-to-end encryption, zero-trust architecture, and compliance with international standards.",benefits:["End-to-end AES-256 encryption","Zero-trust security model","SOC2 and ISO27001 compliance","Multi-factor authentication"],stats:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2"},{label:"Uptime",value:"99.99%"}],features:[{name:"Encryption",description:"Military-grade encryption for all data transmission",specs:["AES-256","TLS 1.3","Key rotation"]},{name:"Access Control",description:"Granular permissions and role-based access control",specs:["RBAC","SSO","MFA"]}]}],i=h(()=>u[d.value]);return(G,o)=>(a(),s("div",y,[o[2]||(o[2]=m('<section class="tech-hero section" data-v-a76ffc32><div class="container" data-v-a76ffc32><div class="hero-content" data-v-a76ffc32><h1 class="hero-title" data-v-a76ffc32> Advanced <span class="text-gradient" data-v-a76ffc32>Technologies</span></h1><p class="hero-subtitle" data-v-a76ffc32> Explore the cutting-edge technologies powering the future of IoT </p></div></div></section>',1)),e("section",b,[e("div",A,[e("div",k,[(a(),s(l,null,r(u,(t,c)=>e("button",{key:t.id,class:f(["tab-button",{active:d.value===c}]),onClick:U=>d.value=c},n(t.name),11,w)),64))]),i.value?(a(),s("div",C,[e("div",S,[e("div",E,[e("h2",L,n(i.value.name),1),e("p",M,n(i.value.description),1),e("div",R,[o[0]||(o[0]=e("h3",null,"Benefits",-1)),e("ul",null,[(a(!0),s(l,null,r(i.value.benefits,t=>(a(),s("li",{key:t},n(t),1))),128))])])]),e("div",T,[e("div",B,[e("div",I,n(i.value.icon),1),e("div",N,[(a(!0),s(l,null,r(i.value.stats,t=>(a(),s("div",{key:t.label,class:"stat-item"},[e("span",x,n(t.value),1),e("span",O,n(t.label),1)]))),128))])])])]),e("div",P,[o[1]||(o[1]=e("h3",null,"Technical Specifications",-1)),e("div",V,[(a(!0),s(l,null,r(i.value.features,t=>(a(),s("div",{key:t.name,class:"feature-card"},[e("h4",null,n(t.name),1),e("p",null,n(t.description),1),e("div",F,[(a(!0),s(l,null,r(t.specs,c=>(a(),s("span",{key:c,class:"spec-tag"},n(c),1))),128))])]))),128))])])])):_("",!0)])])]))}}),K=g(D,[["__scopeId","data-v-a76ffc32"]]);export{K as default};
