import{d,c as n,a as u,b as t,F as r,r as c,e as m,t as s,w as g,f as p,u as v,R as h,o as a,_ as f}from"./index-e7Y3w3wC.js";const _={class:"solutions-view"},y={class:"solutions-grid section"},b={class:"container"},w={class:"solutions-list"},S={class:"solution-icon"},E={class:"solution-title"},k={class:"solution-description"},z={class:"solution-features"},I={class:"solution-stats"},R={class:"stat"},V={class:"stat-value"},x={class:"stat-label"},C=d({__name:"SolutionsView",setup(T){const l=[{id:1,title:"Smart Manufacturing",icon:"🏭",description:"Revolutionize your production with intelligent automation, predictive maintenance, and real-time monitoring.",features:["Predictive maintenance","Quality control automation","Energy optimization","Supply chain visibility"],stat:{value:"40%",label:"Efficiency Increase"}},{id:2,title:"Smart Cities",icon:"🏙️",description:"Build sustainable urban environments with intelligent traffic management, waste optimization, and energy efficiency.",features:["Traffic flow optimization","Smart lighting systems","Waste management","Environmental monitoring"],stat:{value:"30%",label:"Energy Savings"}},{id:3,title:"Healthcare IoT",icon:"🏥",description:"Enhance patient care with remote monitoring, asset tracking, and intelligent healthcare systems.",features:["Patient monitoring","Asset tracking","Environmental control","Emergency response"],stat:{value:"50%",label:"Response Time Improvement"}},{id:4,title:"Smart Agriculture",icon:"🌾",description:"Optimize crop yields with precision farming, soil monitoring, and automated irrigation systems.",features:["Soil monitoring","Automated irrigation","Crop health tracking","Weather integration"],stat:{value:"25%",label:"Yield Increase"}},{id:5,title:"Smart Homes",icon:"🏠",description:"Create intelligent living spaces with automated systems, security, and energy management.",features:["Home automation","Security systems","Energy management","Voice control"],stat:{value:"35%",label:"Energy Reduction"}},{id:6,title:"Fleet Management",icon:"🚛",description:"Optimize logistics with real-time tracking, route optimization, and vehicle health monitoring.",features:["Real-time tracking","Route optimization","Fuel monitoring","Driver behavior analysis"],stat:{value:"20%",label:"Cost Reduction"}}];return(F,i)=>(a(),n("div",_,[i[2]||(i[2]=u('<section class="solutions-hero section" data-v-8ece7cf0><div class="container" data-v-8ece7cf0><div class="hero-content" data-v-8ece7cf0><h1 class="hero-title" data-v-8ece7cf0> IoT <span class="text-gradient" data-v-8ece7cf0>Solutions</span> for Every Industry </h1><p class="hero-subtitle" data-v-8ece7cf0> Discover how our cutting-edge IoT technologies transform businesses across industries </p></div></div></section>',1)),t("section",y,[t("div",b,[t("div",w,[(a(),n(r,null,c(l,e=>t("div",{key:e.id,class:"solution-card"},[t("div",S,s(e.icon),1),t("h3",E,s(e.title),1),t("p",k,s(e.description),1),t("div",z,[i[0]||(i[0]=t("h4",null,"Key Features:",-1)),t("ul",null,[(a(!0),n(r,null,c(e.features,o=>(a(),n("li",{key:o},s(o),1))),128))])]),t("div",I,[t("div",R,[t("span",V,s(e.stat.value),1),t("span",x,s(e.stat.label),1)])]),m(v(h),{to:"/contact",class:"btn btn-outline solution-cta"},{default:g(()=>i[1]||(i[1]=[p(" Learn More ")])),_:1,__:[1]})])),64))])])])]))}}),N=f(C,[["__scopeId","data-v-8ece7cf0"]]);export{N as default};
