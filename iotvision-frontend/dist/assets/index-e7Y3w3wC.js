const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SolutionsView-CVdXMgtn.js","assets/SolutionsView-DFw9Pv31.css","assets/TechnologiesView-dODy-tZv.js","assets/TechnologiesView-BH_NOT_E.css","assets/AboutView-BAYxmzcR.js","assets/AboutView-CtGPWSE_.css","assets/ContactView-DZY8tii4.js","assets/ContactView-CcaztqGz.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ss(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const X={},Ot=[],ze=()=>{},lr=()=>!1,Hn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Es=e=>e.startsWith("onUpdate:"),me=Object.assign,Cs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},cr=Object.prototype.hasOwnProperty,G=(e,t)=>cr.call(e,t),H=Array.isArray,It=e=>fn(e)==="[object Map]",Nn=e=>fn(e)==="[object Set]",Ws=e=>fn(e)==="[object Date]",V=e=>typeof e=="function",ie=e=>typeof e=="string",qe=e=>typeof e=="symbol",te=e=>e!==null&&typeof e=="object",Vi=e=>(te(e)||V(e))&&V(e.then)&&V(e.catch),Bi=Object.prototype.toString,fn=e=>Bi.call(e),ar=e=>fn(e).slice(8,-1),Ui=e=>fn(e)==="[object Object]",Rs=e=>ie(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Gt=Ss(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),jn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ur=/-(\w)/g,gt=jn(e=>e.replace(ur,(t,n)=>n?n.toUpperCase():"")),fr=/\B([A-Z])/g,Et=jn(e=>e.replace(fr,"-$1").toLowerCase()),Ki=jn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jn=jn(e=>e?`on${Ki(e)}`:""),pt=(e,t)=>!Object.is(e,t),bn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Wi=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Pn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let zs;const Vn=()=>zs||(zs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function dn(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=ie(s)?gr(s):dn(s);if(i)for(const o in i)t[o]=i[o]}return t}else if(ie(e)||te(e))return e}const dr=/;(?![^(]*\))/g,hr=/:([^]+)/,pr=/\/\*[^]*?\*\//g;function gr(e){const t={};return e.replace(pr,"").split(dr).forEach(n=>{if(n){const s=n.split(hr);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function le(e){let t="";if(ie(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=le(e[n]);s&&(t+=s+" ")}else if(te(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const mr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",vr=Ss(mr);function zi(e){return!!e||e===""}function _r(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Bn(e[s],t[s]);return n}function Bn(e,t){if(e===t)return!0;let n=Ws(e),s=Ws(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=qe(e),s=qe(t),n||s)return e===t;if(n=H(e),s=H(t),n||s)return n&&s?_r(e,t):!1;if(n=te(e),s=te(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const r in e){const l=e.hasOwnProperty(r),c=t.hasOwnProperty(r);if(l&&!c||!l&&c||!Bn(e[r],t[r]))return!1}}return String(e)===String(t)}function yr(e,t){return e.findIndex(n=>Bn(n,t))}const qi=e=>!!(e&&e.__v_isRef===!0),Se=e=>ie(e)?e:e==null?"":H(e)||te(e)&&(e.toString===Bi||!V(e.toString))?qi(e)?Se(e.value):JSON.stringify(e,Gi,2):String(e),Gi=(e,t)=>qi(t)?Gi(e,t.value):It(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],o)=>(n[Xn(s,o)+" =>"]=i,n),{})}:Nn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xn(n))}:qe(t)?Xn(t):te(t)&&!H(t)&&!Ui(t)?String(t):t,Xn=(e,t="")=>{var n;return qe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let we;class Yi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=we,!t&&we&&(this.index=(we.scopes||(we.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=we;try{return we=this,t()}finally{we=n}}}on(){++this._on===1&&(this.prevScope=we,we=this)}off(){this._on>0&&--this._on===0&&(we=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function br(e){return new Yi(e)}function xr(){return we}let ee;const Zn=new WeakSet;class Qi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,we&&we.active&&we.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Zn.has(this)&&(Zn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Xi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,qs(this),Zi(this);const t=ee,n=$e;ee=this,$e=!0;try{return this.fn()}finally{eo(this),ee=t,$e=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ts(t);this.deps=this.depsTail=void 0,qs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Zn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){us(this)&&this.run()}get dirty(){return us(this)}}let Ji=0,Yt,Qt;function Xi(e,t=!1){if(e.flags|=8,t){e.next=Qt,Qt=e;return}e.next=Yt,Yt=e}function Ps(){Ji++}function As(){if(--Ji>0)return;if(Qt){let t=Qt;for(Qt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Yt;){let t=Yt;for(Yt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Zi(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function eo(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),Ts(s),wr(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function us(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(to(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function to(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===nn)||(e.globalVersion=nn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!us(e))))return;e.flags|=2;const t=e.dep,n=ee,s=$e;ee=e,$e=!0;try{Zi(e);const i=e.fn(e._value);(t.version===0||pt(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{ee=n,$e=s,eo(e),e.flags&=-3}}function Ts(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ts(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function wr(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let $e=!0;const no=[];function st(){no.push($e),$e=!1}function it(){const e=no.pop();$e=e===void 0?!0:e}function qs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ee;ee=void 0;try{t()}finally{ee=n}}}let nn=0;class Sr{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ms{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ee||!$e||ee===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ee)n=this.activeLink=new Sr(ee,this),ee.deps?(n.prevDep=ee.depsTail,ee.depsTail.nextDep=n,ee.depsTail=n):ee.deps=ee.depsTail=n,so(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ee.depsTail,n.nextDep=void 0,ee.depsTail.nextDep=n,ee.depsTail=n,ee.deps===n&&(ee.deps=s)}return n}trigger(t){this.version++,nn++,this.notify(t)}notify(t){Ps();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{As()}}}function so(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)so(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const fs=new WeakMap,St=Symbol(""),ds=Symbol(""),sn=Symbol("");function pe(e,t,n){if($e&&ee){let s=fs.get(e);s||fs.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new Ms),i.map=s,i.key=n),i.track()}}function et(e,t,n,s,i,o){const r=fs.get(e);if(!r){nn++;return}const l=c=>{c&&c.trigger()};if(Ps(),t==="clear")r.forEach(l);else{const c=H(e),u=c&&Rs(n);if(c&&n==="length"){const a=Number(s);r.forEach((h,p)=>{(p==="length"||p===sn||!qe(p)&&p>=a)&&l(h)})}else switch((n!==void 0||r.has(void 0))&&l(r.get(n)),u&&l(r.get(sn)),t){case"add":c?u&&l(r.get("length")):(l(r.get(St)),It(e)&&l(r.get(ds)));break;case"delete":c||(l(r.get(St)),It(e)&&l(r.get(ds)));break;case"set":It(e)&&l(r.get(St));break}}As()}function At(e){const t=q(e);return t===e?t:(pe(t,"iterate",sn),Ie(e)?t:t.map(ue))}function Un(e){return pe(e=q(e),"iterate",sn),e}const Er={__proto__:null,[Symbol.iterator](){return es(this,Symbol.iterator,ue)},concat(...e){return At(this).concat(...e.map(t=>H(t)?At(t):t))},entries(){return es(this,"entries",e=>(e[1]=ue(e[1]),e))},every(e,t){return Je(this,"every",e,t,void 0,arguments)},filter(e,t){return Je(this,"filter",e,t,n=>n.map(ue),arguments)},find(e,t){return Je(this,"find",e,t,ue,arguments)},findIndex(e,t){return Je(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Je(this,"findLast",e,t,ue,arguments)},findLastIndex(e,t){return Je(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Je(this,"forEach",e,t,void 0,arguments)},includes(...e){return ts(this,"includes",e)},indexOf(...e){return ts(this,"indexOf",e)},join(e){return At(this).join(e)},lastIndexOf(...e){return ts(this,"lastIndexOf",e)},map(e,t){return Je(this,"map",e,t,void 0,arguments)},pop(){return Kt(this,"pop")},push(...e){return Kt(this,"push",e)},reduce(e,...t){return Gs(this,"reduce",e,t)},reduceRight(e,...t){return Gs(this,"reduceRight",e,t)},shift(){return Kt(this,"shift")},some(e,t){return Je(this,"some",e,t,void 0,arguments)},splice(...e){return Kt(this,"splice",e)},toReversed(){return At(this).toReversed()},toSorted(e){return At(this).toSorted(e)},toSpliced(...e){return At(this).toSpliced(...e)},unshift(...e){return Kt(this,"unshift",e)},values(){return es(this,"values",ue)}};function es(e,t,n){const s=Un(e),i=s[t]();return s!==e&&!Ie(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const Cr=Array.prototype;function Je(e,t,n,s,i,o){const r=Un(e),l=r!==e&&!Ie(e),c=r[t];if(c!==Cr[t]){const h=c.apply(e,o);return l?ue(h):h}let u=n;r!==e&&(l?u=function(h,p){return n.call(this,ue(h),p,e)}:n.length>2&&(u=function(h,p){return n.call(this,h,p,e)}));const a=c.call(r,u,s);return l&&i?i(a):a}function Gs(e,t,n,s){const i=Un(e);let o=n;return i!==e&&(Ie(e)?n.length>3&&(o=function(r,l,c){return n.call(this,r,l,c,e)}):o=function(r,l,c){return n.call(this,r,ue(l),c,e)}),i[t](o,...s)}function ts(e,t,n){const s=q(e);pe(s,"iterate",sn);const i=s[t](...n);return(i===-1||i===!1)&&$s(n[0])?(n[0]=q(n[0]),s[t](...n)):i}function Kt(e,t,n=[]){st(),Ps();const s=q(e)[t].apply(e,n);return As(),it(),s}const Rr=Ss("__proto__,__v_isRef,__isVue"),io=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qe));function Pr(e){qe(e)||(e=String(e));const t=q(this);return pe(t,"has",e),t.hasOwnProperty(e)}class oo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(i?o?Dr:ao:o?co:lo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=H(t);if(!i){let c;if(r&&(c=Er[n]))return c;if(n==="hasOwnProperty")return Pr}const l=Reflect.get(t,n,ge(t)?t:s);return(qe(n)?io.has(n):Rr(n))||(i||pe(t,"get",n),o)?l:ge(l)?r&&Rs(n)?l:l.value:te(l)?i?fo(l):Kn(l):l}}class ro extends oo{constructor(t=!1){super(!1,t)}set(t,n,s,i){let o=t[n];if(!this._isShallow){const c=mt(o);if(!Ie(s)&&!mt(s)&&(o=q(o),s=q(s)),!H(t)&&ge(o)&&!ge(s))return c?!1:(o.value=s,!0)}const r=H(t)&&Rs(n)?Number(n)<t.length:G(t,n),l=Reflect.set(t,n,s,ge(t)?t:i);return t===q(i)&&(r?pt(s,o)&&et(t,"set",n,s):et(t,"add",n,s)),l}deleteProperty(t,n){const s=G(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&et(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!qe(n)||!io.has(n))&&pe(t,"has",n),s}ownKeys(t){return pe(t,"iterate",H(t)?"length":St),Reflect.ownKeys(t)}}class Ar extends oo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Tr=new ro,Mr=new Ar,Or=new ro(!0);const hs=e=>e,mn=e=>Reflect.getPrototypeOf(e);function Ir(e,t,n){return function(...s){const i=this.__v_raw,o=q(i),r=It(o),l=e==="entries"||e===Symbol.iterator&&r,c=e==="keys"&&r,u=i[e](...s),a=n?hs:t?An:ue;return!t&&pe(o,"iterate",c?ds:St),{next(){const{value:h,done:p}=u.next();return p?{value:h,done:p}:{value:l?[a(h[0]),a(h[1])]:a(h),done:p}},[Symbol.iterator](){return this}}}}function vn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function $r(e,t){const n={get(i){const o=this.__v_raw,r=q(o),l=q(i);e||(pt(i,l)&&pe(r,"get",i),pe(r,"get",l));const{has:c}=mn(r),u=t?hs:e?An:ue;if(c.call(r,i))return u(o.get(i));if(c.call(r,l))return u(o.get(l));o!==r&&o.get(i)},get size(){const i=this.__v_raw;return!e&&pe(q(i),"iterate",St),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,r=q(o),l=q(i);return e||(pt(i,l)&&pe(r,"has",i),pe(r,"has",l)),i===l?o.has(i):o.has(i)||o.has(l)},forEach(i,o){const r=this,l=r.__v_raw,c=q(l),u=t?hs:e?An:ue;return!e&&pe(c,"iterate",St),l.forEach((a,h)=>i.call(o,u(a),u(h),r))}};return me(n,e?{add:vn("add"),set:vn("set"),delete:vn("delete"),clear:vn("clear")}:{add(i){!t&&!Ie(i)&&!mt(i)&&(i=q(i));const o=q(this);return mn(o).has.call(o,i)||(o.add(i),et(o,"add",i,i)),this},set(i,o){!t&&!Ie(o)&&!mt(o)&&(o=q(o));const r=q(this),{has:l,get:c}=mn(r);let u=l.call(r,i);u||(i=q(i),u=l.call(r,i));const a=c.call(r,i);return r.set(i,o),u?pt(o,a)&&et(r,"set",i,o):et(r,"add",i,o),this},delete(i){const o=q(this),{has:r,get:l}=mn(o);let c=r.call(o,i);c||(i=q(i),c=r.call(o,i)),l&&l.call(o,i);const u=o.delete(i);return c&&et(o,"delete",i,void 0),u},clear(){const i=q(this),o=i.size!==0,r=i.clear();return o&&et(i,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Ir(i,e,t)}),n}function Os(e,t){const n=$r(e,t);return(s,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(G(n,i)&&i in s?n:s,i,o)}const Fr={get:Os(!1,!1)},kr={get:Os(!1,!0)},Lr={get:Os(!0,!1)};const lo=new WeakMap,co=new WeakMap,ao=new WeakMap,Dr=new WeakMap;function Hr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Nr(e){return e.__v_skip||!Object.isExtensible(e)?0:Hr(ar(e))}function Kn(e){return mt(e)?e:Is(e,!1,Tr,Fr,lo)}function uo(e){return Is(e,!1,Or,kr,co)}function fo(e){return Is(e,!0,Mr,Lr,ao)}function Is(e,t,n,s,i){if(!te(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Nr(e);if(o===0)return e;const r=i.get(e);if(r)return r;const l=new Proxy(e,o===2?s:n);return i.set(e,l),l}function $t(e){return mt(e)?$t(e.__v_raw):!!(e&&e.__v_isReactive)}function mt(e){return!!(e&&e.__v_isReadonly)}function Ie(e){return!!(e&&e.__v_isShallow)}function $s(e){return e?!!e.__v_raw:!1}function q(e){const t=e&&e.__v_raw;return t?q(t):e}function ho(e){return!G(e,"__v_skip")&&Object.isExtensible(e)&&Wi(e,"__v_skip",!0),e}const ue=e=>te(e)?Kn(e):e,An=e=>te(e)?fo(e):e;function ge(e){return e?e.__v_isRef===!0:!1}function Ge(e){return po(e,!1)}function jr(e){return po(e,!0)}function po(e,t){return ge(e)?e:new Vr(e,t)}class Vr{constructor(t,n){this.dep=new Ms,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:q(t),this._value=n?t:ue(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ie(t)||mt(t);t=s?t:q(t),pt(t,n)&&(this._rawValue=t,this._value=s?t:ue(t),this.dep.trigger())}}function se(e){return ge(e)?e.value:e}const Br={get:(e,t,n)=>t==="__v_raw"?e:se(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return ge(i)&&!ge(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function go(e){return $t(e)?e:new Proxy(e,Br)}class Ur{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ms(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=nn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ee!==this)return Xi(this,!0),!0}get value(){const t=this.dep.track();return to(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Kr(e,t,n=!1){let s,i;return V(e)?s=e:(s=e.get,i=e.set),new Ur(s,i,n)}const _n={},Tn=new WeakMap;let xt;function Wr(e,t=!1,n=xt){if(n){let s=Tn.get(n);s||Tn.set(n,s=[]),s.push(e)}}function zr(e,t,n=X){const{immediate:s,deep:i,once:o,scheduler:r,augmentJob:l,call:c}=n,u=I=>i?I:Ie(I)||i===!1||i===0?tt(I,1):tt(I);let a,h,p,m,P=!1,O=!1;if(ge(e)?(h=()=>e.value,P=Ie(e)):$t(e)?(h=()=>u(e),P=!0):H(e)?(O=!0,P=e.some(I=>$t(I)||Ie(I)),h=()=>e.map(I=>{if(ge(I))return I.value;if($t(I))return u(I);if(V(I))return c?c(I,2):I()})):V(e)?t?h=c?()=>c(e,2):e:h=()=>{if(p){st();try{p()}finally{it()}}const I=xt;xt=a;try{return c?c(e,3,[m]):e(m)}finally{xt=I}}:h=ze,t&&i){const I=h,K=i===!0?1/0:i;h=()=>tt(I(),K)}const N=xr(),w=()=>{a.stop(),N&&N.active&&Cs(N.effects,a)};if(o&&t){const I=t;t=(...K)=>{I(...K),w()}}let M=O?new Array(e.length).fill(_n):_n;const F=I=>{if(!(!(a.flags&1)||!a.dirty&&!I))if(t){const K=a.run();if(i||P||(O?K.some((oe,ne)=>pt(oe,M[ne])):pt(K,M))){p&&p();const oe=xt;xt=a;try{const ne=[K,M===_n?void 0:O&&M[0]===_n?[]:M,m];M=K,c?c(t,3,ne):t(...ne)}finally{xt=oe}}}else a.run()};return l&&l(F),a=new Qi(h),a.scheduler=r?()=>r(F,!1):F,m=I=>Wr(I,!1,a),p=a.onStop=()=>{const I=Tn.get(a);if(I){if(c)c(I,4);else for(const K of I)K();Tn.delete(a)}},t?s?F(!0):M=a.run():r?r(F.bind(null,!0),!0):a.run(),w.pause=a.pause.bind(a),w.resume=a.resume.bind(a),w.stop=w,w}function tt(e,t=1/0,n){if(t<=0||!te(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ge(e))tt(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)tt(e[s],t,n);else if(Nn(e)||It(e))e.forEach(s=>{tt(s,t,n)});else if(Ui(e)){for(const s in e)tt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&tt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function hn(e,t,n,s){try{return s?e(...s):e()}catch(i){Wn(i,t,n)}}function Ye(e,t,n,s){if(V(e)){const i=hn(e,t,n,s);return i&&Vi(i)&&i.catch(o=>{Wn(o,t,n)}),i}if(H(e)){const i=[];for(let o=0;o<e.length;o++)i.push(Ye(e[o],t,n,s));return i}}function Wn(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||X;if(t){let l=t.parent;const c=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,u)===!1)return}l=l.parent}if(o){st(),hn(o,null,10,[e,c,u]),it();return}}qr(e,n,i,s,r)}function qr(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const _e=[];let Ke=-1;const Ft=[];let ft=null,Tt=0;const mo=Promise.resolve();let Mn=null;function Fs(e){const t=Mn||mo;return e?t.then(this?e.bind(this):e):t}function Gr(e){let t=Ke+1,n=_e.length;for(;t<n;){const s=t+n>>>1,i=_e[s],o=on(i);o<e||o===e&&i.flags&2?t=s+1:n=s}return t}function ks(e){if(!(e.flags&1)){const t=on(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=on(n)?_e.push(e):_e.splice(Gr(t),0,e),e.flags|=1,vo()}}function vo(){Mn||(Mn=mo.then(yo))}function Yr(e){H(e)?Ft.push(...e):ft&&e.id===-1?ft.splice(Tt+1,0,e):e.flags&1||(Ft.push(e),e.flags|=1),vo()}function Ys(e,t,n=Ke+1){for(;n<_e.length;n++){const s=_e[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;_e.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function _o(e){if(Ft.length){const t=[...new Set(Ft)].sort((n,s)=>on(n)-on(s));if(Ft.length=0,ft){ft.push(...t);return}for(ft=t,Tt=0;Tt<ft.length;Tt++){const n=ft[Tt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ft=null,Tt=0}}const on=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yo(e){try{for(Ke=0;Ke<_e.length;Ke++){const t=_e[Ke];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),hn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ke<_e.length;Ke++){const t=_e[Ke];t&&(t.flags&=-2)}Ke=-1,_e.length=0,_o(),Mn=null,(_e.length||Ft.length)&&yo()}}let Oe=null,bo=null;function On(e){const t=Oe;return Oe=e,bo=e&&e.type.__scopeId||null,t}function fe(e,t=Oe,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&ii(-1);const o=On(t);let r;try{r=e(...i)}finally{On(o),s._d&&ii(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function sf(e,t){if(Oe===null)return e;const n=Yn(Oe),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,r,l,c=X]=t[i];o&&(V(o)&&(o={mounted:o,updated:o}),o.deep&&tt(r),s.push({dir:o,instance:n,value:r,oldValue:void 0,arg:l,modifiers:c}))}return e}function yt(e,t,n,s){const i=e.dirs,o=t&&t.dirs;for(let r=0;r<i.length;r++){const l=i[r];o&&(l.oldValue=o[r].value);let c=l.dir[s];c&&(st(),Ye(c,n,8,[e.el,l,e,t]),it())}}const Qr=Symbol("_vte"),Jr=e=>e.__isTeleport;function Ls(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ls(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ke(e,t){return V(e)?me({name:e.name},t,{setup:e}):e}function xo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function In(e,t,n,s,i=!1){if(H(e)){e.forEach((P,O)=>In(P,t&&(H(t)?t[O]:t),n,s,i));return}if(Jt(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&In(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Yn(s.component):s.el,r=i?null:o,{i:l,r:c}=e,u=t&&t.r,a=l.refs===X?l.refs={}:l.refs,h=l.setupState,p=q(h),m=h===X?()=>!1:P=>G(p,P);if(u!=null&&u!==c&&(ie(u)?(a[u]=null,m(u)&&(h[u]=null)):ge(u)&&(u.value=null)),V(c))hn(c,l,12,[r,a]);else{const P=ie(c),O=ge(c);if(P||O){const N=()=>{if(e.f){const w=P?m(c)?h[c]:a[c]:c.value;i?H(w)&&Cs(w,o):H(w)?w.includes(o)||w.push(o):P?(a[c]=[o],m(c)&&(h[c]=a[c])):(c.value=[o],e.k&&(a[e.k]=c.value))}else P?(a[c]=r,m(c)&&(h[c]=r)):O&&(c.value=r,e.k&&(a[e.k]=r))};r?(N.id=-1,Re(N,n)):N()}}}Vn().requestIdleCallback;Vn().cancelIdleCallback;const Jt=e=>!!e.type.__asyncLoader,wo=e=>e.type.__isKeepAlive;function Xr(e,t){So(e,"a",t)}function Zr(e,t){So(e,"da",t)}function So(e,t,n=ye){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(zn(t,s,n),n){let i=n.parent;for(;i&&i.parent;)wo(i.parent.vnode)&&el(s,t,n,i),i=i.parent}}function el(e,t,n,s){const i=zn(t,e,s,!0);Vt(()=>{Cs(s[t],i)},n)}function zn(e,t,n=ye,s=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...r)=>{st();const l=pn(n),c=Ye(t,n,e,r);return l(),it(),c});return s?i.unshift(o):i.push(o),o}}const ot=e=>(t,n=ye)=>{(!cn||e==="sp")&&zn(e,(...s)=>t(...s),n)},tl=ot("bm"),jt=ot("m"),nl=ot("bu"),sl=ot("u"),il=ot("bum"),Vt=ot("um"),ol=ot("sp"),rl=ot("rtg"),ll=ot("rtc");function cl(e,t=ye){zn("ec",e,t)}const al=Symbol.for("v-ndc");function xn(e,t,n,s){let i;const o=n,r=H(e);if(r||ie(e)){const l=r&&$t(e);let c=!1,u=!1;l&&(c=!Ie(e),u=mt(e),e=Un(e)),i=new Array(e.length);for(let a=0,h=e.length;a<h;a++)i[a]=t(c?u?An(ue(e[a])):ue(e[a]):e[a],a,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,o)}else if(te(e))if(e[Symbol.iterator])i=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);i=new Array(l.length);for(let c=0,u=l.length;c<u;c++){const a=l[c];i[c]=t(e[a],a,c,o)}}else i=[];return i}const ps=e=>e?Uo(e)?Yn(e):ps(e.parent):null,Xt=me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ps(e.parent),$root:e=>ps(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Co(e),$forceUpdate:e=>e.f||(e.f=()=>{ks(e.update)}),$nextTick:e=>e.n||(e.n=Fs.bind(e.proxy)),$watch:e=>Ml.bind(e)}),ns=(e,t)=>e!==X&&!e.__isScriptSetup&&G(e,t),ul={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:o,accessCache:r,type:l,appContext:c}=e;let u;if(t[0]!=="$"){const m=r[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(ns(s,t))return r[t]=1,s[t];if(i!==X&&G(i,t))return r[t]=2,i[t];if((u=e.propsOptions[0])&&G(u,t))return r[t]=3,o[t];if(n!==X&&G(n,t))return r[t]=4,n[t];gs&&(r[t]=0)}}const a=Xt[t];let h,p;if(a)return t==="$attrs"&&pe(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==X&&G(n,t))return r[t]=4,n[t];if(p=c.config.globalProperties,G(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:o}=e;return ns(i,t)?(i[t]=n,!0):s!==X&&G(s,t)?(s[t]=n,!0):G(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:o}},r){let l;return!!n[r]||e!==X&&G(e,r)||ns(t,r)||(l=o[0])&&G(l,r)||G(s,r)||G(Xt,r)||G(i.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:G(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Qs(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gs=!0;function fl(e){const t=Co(e),n=e.proxy,s=e.ctx;gs=!1,t.beforeCreate&&Js(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:r,watch:l,provide:c,inject:u,created:a,beforeMount:h,mounted:p,beforeUpdate:m,updated:P,activated:O,deactivated:N,beforeDestroy:w,beforeUnmount:M,destroyed:F,unmounted:I,render:K,renderTracked:oe,renderTriggered:ne,errorCaptured:Le,serverPrefetch:lt,expose:De,inheritAttrs:ct,components:_t,directives:He,filters:Bt}=t;if(u&&dl(u,s,null),r)for(const Q in r){const W=r[Q];V(W)&&(s[Q]=W.bind(n))}if(i){const Q=i.call(n,n);te(Q)&&(e.data=Kn(Q))}if(gs=!0,o)for(const Q in o){const W=o[Q],Qe=V(W)?W.bind(n,n):V(W.get)?W.get.bind(n,n):ze,at=!V(W)&&V(W.set)?W.set.bind(n):ze,Ne=Ae({get:Qe,set:at});Object.defineProperty(s,Q,{enumerable:!0,configurable:!0,get:()=>Ne.value,set:be=>Ne.value=be})}if(l)for(const Q in l)Eo(l[Q],s,n,Q);if(c){const Q=V(c)?c.call(n):c;Reflect.ownKeys(Q).forEach(W=>{wn(W,Q[W])})}a&&Js(a,e,"c");function re(Q,W){H(W)?W.forEach(Qe=>Q(Qe.bind(n))):W&&Q(W.bind(n))}if(re(tl,h),re(jt,p),re(nl,m),re(sl,P),re(Xr,O),re(Zr,N),re(cl,Le),re(ll,oe),re(rl,ne),re(il,M),re(Vt,I),re(ol,lt),H(De))if(De.length){const Q=e.exposed||(e.exposed={});De.forEach(W=>{Object.defineProperty(Q,W,{get:()=>n[W],set:Qe=>n[W]=Qe})})}else e.exposed||(e.exposed={});K&&e.render===ze&&(e.render=K),ct!=null&&(e.inheritAttrs=ct),_t&&(e.components=_t),He&&(e.directives=He),lt&&xo(e)}function dl(e,t,n=ze){H(e)&&(e=ms(e));for(const s in e){const i=e[s];let o;te(i)?"default"in i?o=nt(i.from||s,i.default,!0):o=nt(i.from||s):o=nt(i),ge(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function Js(e,t,n){Ye(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Eo(e,t,n,s){let i=s.includes(".")?Ho(n,s):()=>n[s];if(ie(e)){const o=t[e];V(o)&&Sn(i,o)}else if(V(e))Sn(i,e.bind(n));else if(te(e))if(H(e))e.forEach(o=>Eo(o,t,n,s));else{const o=V(e.handler)?e.handler.bind(n):t[e.handler];V(o)&&Sn(i,o,e)}}function Co(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,l=o.get(t);let c;return l?c=l:!i.length&&!n&&!s?c=t:(c={},i.length&&i.forEach(u=>$n(c,u,r,!0)),$n(c,t,r)),te(t)&&o.set(t,c),c}function $n(e,t,n,s=!1){const{mixins:i,extends:o}=t;o&&$n(e,o,n,!0),i&&i.forEach(r=>$n(e,r,n,!0));for(const r in t)if(!(s&&r==="expose")){const l=hl[r]||n&&n[r];e[r]=l?l(e[r],t[r]):t[r]}return e}const hl={data:Xs,props:Zs,emits:Zs,methods:qt,computed:qt,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:qt,directives:qt,watch:gl,provide:Xs,inject:pl};function Xs(e,t){return t?e?function(){return me(V(e)?e.call(this,this):e,V(t)?t.call(this,this):t)}:t:e}function pl(e,t){return qt(ms(e),ms(t))}function ms(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function qt(e,t){return e?me(Object.create(null),e,t):t}function Zs(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:me(Object.create(null),Qs(e),Qs(t??{})):t}function gl(e,t){if(!e)return t;if(!t)return e;const n=me(Object.create(null),e);for(const s in t)n[s]=ve(e[s],t[s]);return n}function Ro(){return{app:null,config:{isNativeTag:lr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ml=0;function vl(e,t){return function(s,i=null){V(s)||(s=me({},s)),i!=null&&!te(i)&&(i=null);const o=Ro(),r=new WeakSet,l=[];let c=!1;const u=o.app={_uid:ml++,_component:s,_props:i,_container:null,_context:o,_instance:null,version:Xl,get config(){return o.config},set config(a){},use(a,...h){return r.has(a)||(a&&V(a.install)?(r.add(a),a.install(u,...h)):V(a)&&(r.add(a),a(u,...h))),u},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),u},component(a,h){return h?(o.components[a]=h,u):o.components[a]},directive(a,h){return h?(o.directives[a]=h,u):o.directives[a]},mount(a,h,p){if(!c){const m=u._ceVNode||B(s,i);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,a,p),c=!0,u._container=a,a.__vue_app__=u,Yn(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(Ye(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,h){return o.provides[a]=h,u},runWithContext(a){const h=kt;kt=u;try{return a()}finally{kt=h}}};return u}}let kt=null;function wn(e,t){if(ye){let n=ye.provides;const s=ye.parent&&ye.parent.provides;s===n&&(n=ye.provides=Object.create(s)),n[e]=t}}function nt(e,t,n=!1){const s=ye||Oe;if(s||kt){let i=kt?kt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&V(t)?t.call(s&&s.proxy):t}}const Po={},Ao=()=>Object.create(Po),To=e=>Object.getPrototypeOf(e)===Po;function _l(e,t,n,s=!1){const i={},o=Ao();e.propsDefaults=Object.create(null),Mo(e,t,i,o);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);n?e.props=s?i:uo(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function yl(e,t,n,s){const{props:i,attrs:o,vnode:{patchFlag:r}}=e,l=q(i),[c]=e.propsOptions;let u=!1;if((s||r>0)&&!(r&16)){if(r&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let p=a[h];if(qn(e.emitsOptions,p))continue;const m=t[p];if(c)if(G(o,p))m!==o[p]&&(o[p]=m,u=!0);else{const P=gt(p);i[P]=vs(c,l,P,m,e,!1)}else m!==o[p]&&(o[p]=m,u=!0)}}}else{Mo(e,t,i,o)&&(u=!0);let a;for(const h in l)(!t||!G(t,h)&&((a=Et(h))===h||!G(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(i[h]=vs(c,l,h,void 0,e,!0)):delete i[h]);if(o!==l)for(const h in o)(!t||!G(t,h))&&(delete o[h],u=!0)}u&&et(e.attrs,"set","")}function Mo(e,t,n,s){const[i,o]=e.propsOptions;let r=!1,l;if(t)for(let c in t){if(Gt(c))continue;const u=t[c];let a;i&&G(i,a=gt(c))?!o||!o.includes(a)?n[a]=u:(l||(l={}))[a]=u:qn(e.emitsOptions,c)||(!(c in s)||u!==s[c])&&(s[c]=u,r=!0)}if(o){const c=q(n),u=l||X;for(let a=0;a<o.length;a++){const h=o[a];n[h]=vs(i,c,h,u[h],e,!G(u,h))}}return r}function vs(e,t,n,s,i,o){const r=e[n];if(r!=null){const l=G(r,"default");if(l&&s===void 0){const c=r.default;if(r.type!==Function&&!r.skipFactory&&V(c)){const{propsDefaults:u}=i;if(n in u)s=u[n];else{const a=pn(i);s=u[n]=c.call(null,t),a()}}else s=c;i.ce&&i.ce._setProp(n,s)}r[0]&&(o&&!l?s=!1:r[1]&&(s===""||s===Et(n))&&(s=!0))}return s}const bl=new WeakMap;function Oo(e,t,n=!1){const s=n?bl:t.propsCache,i=s.get(e);if(i)return i;const o=e.props,r={},l=[];let c=!1;if(!V(e)){const a=h=>{c=!0;const[p,m]=Oo(h,t,!0);me(r,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return te(e)&&s.set(e,Ot),Ot;if(H(o))for(let a=0;a<o.length;a++){const h=gt(o[a]);ei(h)&&(r[h]=X)}else if(o)for(const a in o){const h=gt(a);if(ei(h)){const p=o[a],m=r[h]=H(p)||V(p)?{type:p}:me({},p),P=m.type;let O=!1,N=!0;if(H(P))for(let w=0;w<P.length;++w){const M=P[w],F=V(M)&&M.name;if(F==="Boolean"){O=!0;break}else F==="String"&&(N=!1)}else O=V(P)&&P.name==="Boolean";m[0]=O,m[1]=N,(O||G(m,"default"))&&l.push(h)}}const u=[r,l];return te(e)&&s.set(e,u),u}function ei(e){return e[0]!=="$"&&!Gt(e)}const Ds=e=>e[0]==="_"||e==="$stable",Hs=e=>H(e)?e.map(We):[We(e)],xl=(e,t,n)=>{if(t._n)return t;const s=fe((...i)=>Hs(t(...i)),n);return s._c=!1,s},Io=(e,t,n)=>{const s=e._ctx;for(const i in e){if(Ds(i))continue;const o=e[i];if(V(o))t[i]=xl(i,o,s);else if(o!=null){const r=Hs(o);t[i]=()=>r}}},$o=(e,t)=>{const n=Hs(t);e.slots.default=()=>n},Fo=(e,t,n)=>{for(const s in t)(n||!Ds(s))&&(e[s]=t[s])},wl=(e,t,n)=>{const s=e.slots=Ao();if(e.vnode.shapeFlag&32){const i=t._;i?(Fo(s,t,n),n&&Wi(s,"_",i,!0)):Io(t,s)}else t&&$o(e,t)},Sl=(e,t,n)=>{const{vnode:s,slots:i}=e;let o=!0,r=X;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Fo(i,t,n):(o=!t.$stable,Io(t,i)),r=t}else t&&($o(e,t),r={default:1});if(o)for(const l in i)!Ds(l)&&r[l]==null&&delete i[l]},Re=Dl;function El(e){return Cl(e)}function Cl(e,t){const n=Vn();n.__VUE__=!0;const{insert:s,remove:i,patchProp:o,createElement:r,createText:l,createComment:c,setText:u,setElementText:a,parentNode:h,nextSibling:p,setScopeId:m=ze,insertStaticContent:P}=e,O=(f,d,g,_=null,x=null,b=null,R=void 0,C=null,E=!!d.dynamicChildren)=>{if(f===d)return;f&&!Wt(f,d)&&(_=y(f),be(f,x,b,!0),f=null),d.patchFlag===-2&&(E=!1,d.dynamicChildren=null);const{type:S,ref:D,shapeFlag:T}=d;switch(S){case Gn:N(f,d,g,_);break;case vt:w(f,d,g,_);break;case En:f==null&&M(d,g,_,R);break;case Pe:_t(f,d,g,_,x,b,R,C,E);break;default:T&1?K(f,d,g,_,x,b,R,C,E):T&6?He(f,d,g,_,x,b,R,C,E):(T&64||T&128)&&S.process(f,d,g,_,x,b,R,C,E,k)}D!=null&&x&&In(D,f&&f.ref,b,d||f,!d)},N=(f,d,g,_)=>{if(f==null)s(d.el=l(d.children),g,_);else{const x=d.el=f.el;d.children!==f.children&&u(x,d.children)}},w=(f,d,g,_)=>{f==null?s(d.el=c(d.children||""),g,_):d.el=f.el},M=(f,d,g,_)=>{[f.el,f.anchor]=P(f.children,d,g,_,f.el,f.anchor)},F=({el:f,anchor:d},g,_)=>{let x;for(;f&&f!==d;)x=p(f),s(f,g,_),f=x;s(d,g,_)},I=({el:f,anchor:d})=>{let g;for(;f&&f!==d;)g=p(f),i(f),f=g;i(d)},K=(f,d,g,_,x,b,R,C,E)=>{d.type==="svg"?R="svg":d.type==="math"&&(R="mathml"),f==null?oe(d,g,_,x,b,R,C,E):lt(f,d,x,b,R,C,E)},oe=(f,d,g,_,x,b,R,C)=>{let E,S;const{props:D,shapeFlag:T,transition:L,dirs:j}=f;if(E=f.el=r(f.type,b,D&&D.is,D),T&8?a(E,f.children):T&16&&Le(f.children,E,null,_,x,ss(f,b),R,C),j&&yt(f,null,_,"created"),ne(E,f,f.scopeId,R,_),D){for(const Z in D)Z!=="value"&&!Gt(Z)&&o(E,Z,null,D[Z],b,_);"value"in D&&o(E,"value",null,D.value,b),(S=D.onVnodeBeforeMount)&&Ue(S,_,f)}j&&yt(f,null,_,"beforeMount");const U=Rl(x,L);U&&L.beforeEnter(E),s(E,d,g),((S=D&&D.onVnodeMounted)||U||j)&&Re(()=>{S&&Ue(S,_,f),U&&L.enter(E),j&&yt(f,null,_,"mounted")},x)},ne=(f,d,g,_,x)=>{if(g&&m(f,g),_)for(let b=0;b<_.length;b++)m(f,_[b]);if(x){let b=x.subTree;if(d===b||jo(b.type)&&(b.ssContent===d||b.ssFallback===d)){const R=x.vnode;ne(f,R,R.scopeId,R.slotScopeIds,x.parent)}}},Le=(f,d,g,_,x,b,R,C,E=0)=>{for(let S=E;S<f.length;S++){const D=f[S]=C?dt(f[S]):We(f[S]);O(null,D,d,g,_,x,b,R,C)}},lt=(f,d,g,_,x,b,R)=>{const C=d.el=f.el;let{patchFlag:E,dynamicChildren:S,dirs:D}=d;E|=f.patchFlag&16;const T=f.props||X,L=d.props||X;let j;if(g&&bt(g,!1),(j=L.onVnodeBeforeUpdate)&&Ue(j,g,d,f),D&&yt(d,f,g,"beforeUpdate"),g&&bt(g,!0),(T.innerHTML&&L.innerHTML==null||T.textContent&&L.textContent==null)&&a(C,""),S?De(f.dynamicChildren,S,C,g,_,ss(d,x),b):R||W(f,d,C,null,g,_,ss(d,x),b,!1),E>0){if(E&16)ct(C,T,L,g,x);else if(E&2&&T.class!==L.class&&o(C,"class",null,L.class,x),E&4&&o(C,"style",T.style,L.style,x),E&8){const U=d.dynamicProps;for(let Z=0;Z<U.length;Z++){const Y=U[Z],Ee=T[Y],xe=L[Y];(xe!==Ee||Y==="value")&&o(C,Y,Ee,xe,x,g)}}E&1&&f.children!==d.children&&a(C,d.children)}else!R&&S==null&&ct(C,T,L,g,x);((j=L.onVnodeUpdated)||D)&&Re(()=>{j&&Ue(j,g,d,f),D&&yt(d,f,g,"updated")},_)},De=(f,d,g,_,x,b,R)=>{for(let C=0;C<d.length;C++){const E=f[C],S=d[C],D=E.el&&(E.type===Pe||!Wt(E,S)||E.shapeFlag&198)?h(E.el):g;O(E,S,D,null,_,x,b,R,!0)}},ct=(f,d,g,_,x)=>{if(d!==g){if(d!==X)for(const b in d)!Gt(b)&&!(b in g)&&o(f,b,d[b],null,x,_);for(const b in g){if(Gt(b))continue;const R=g[b],C=d[b];R!==C&&b!=="value"&&o(f,b,C,R,x,_)}"value"in g&&o(f,"value",d.value,g.value,x)}},_t=(f,d,g,_,x,b,R,C,E)=>{const S=d.el=f?f.el:l(""),D=d.anchor=f?f.anchor:l("");let{patchFlag:T,dynamicChildren:L,slotScopeIds:j}=d;j&&(C=C?C.concat(j):j),f==null?(s(S,g,_),s(D,g,_),Le(d.children||[],g,D,x,b,R,C,E)):T>0&&T&64&&L&&f.dynamicChildren?(De(f.dynamicChildren,L,g,x,b,R,C),(d.key!=null||x&&d===x.subTree)&&ko(f,d,!0)):W(f,d,g,D,x,b,R,C,E)},He=(f,d,g,_,x,b,R,C,E)=>{d.slotScopeIds=C,f==null?d.shapeFlag&512?x.ctx.activate(d,g,_,R,E):Bt(d,g,_,x,b,R,E):Ct(f,d,E)},Bt=(f,d,g,_,x,b,R)=>{const C=f.component=zl(f,_,x);if(wo(f)&&(C.ctx.renderer=k),ql(C,!1,R),C.asyncDep){if(x&&x.registerDep(C,re,R),!f.el){const E=C.subTree=B(vt);w(null,E,d,g)}}else re(C,f,d,g,x,b,R)},Ct=(f,d,g)=>{const _=d.component=f.component;if(kl(f,d,g))if(_.asyncDep&&!_.asyncResolved){Q(_,d,g);return}else _.next=d,_.update();else d.el=f.el,_.vnode=d},re=(f,d,g,_,x,b,R)=>{const C=()=>{if(f.isMounted){let{next:T,bu:L,u:j,parent:U,vnode:Z}=f;{const Ve=Lo(f);if(Ve){T&&(T.el=Z.el,Q(f,T,R)),Ve.asyncDep.then(()=>{f.isUnmounted||C()});return}}let Y=T,Ee;bt(f,!1),T?(T.el=Z.el,Q(f,T,R)):T=Z,L&&bn(L),(Ee=T.props&&T.props.onVnodeBeforeUpdate)&&Ue(Ee,U,T,Z),bt(f,!0);const xe=ni(f),je=f.subTree;f.subTree=xe,O(je,xe,h(je.el),y(je),f,x,b),T.el=xe.el,Y===null&&Ll(f,xe.el),j&&Re(j,x),(Ee=T.props&&T.props.onVnodeUpdated)&&Re(()=>Ue(Ee,U,T,Z),x)}else{let T;const{el:L,props:j}=d,{bm:U,m:Z,parent:Y,root:Ee,type:xe}=f,je=Jt(d);bt(f,!1),U&&bn(U),!je&&(T=j&&j.onVnodeBeforeMount)&&Ue(T,Y,d),bt(f,!0);{Ee.ce&&Ee.ce._injectChildStyle(xe);const Ve=f.subTree=ni(f);O(null,Ve,g,_,f,x,b),d.el=Ve.el}if(Z&&Re(Z,x),!je&&(T=j&&j.onVnodeMounted)){const Ve=d;Re(()=>Ue(T,Y,Ve),x)}(d.shapeFlag&256||Y&&Jt(Y.vnode)&&Y.vnode.shapeFlag&256)&&f.a&&Re(f.a,x),f.isMounted=!0,d=g=_=null}};f.scope.on();const E=f.effect=new Qi(C);f.scope.off();const S=f.update=E.run.bind(E),D=f.job=E.runIfDirty.bind(E);D.i=f,D.id=f.uid,E.scheduler=()=>ks(D),bt(f,!0),S()},Q=(f,d,g)=>{d.component=f;const _=f.vnode.props;f.vnode=d,f.next=null,yl(f,d.props,_,g),Sl(f,d.children,g),st(),Ys(f),it()},W=(f,d,g,_,x,b,R,C,E=!1)=>{const S=f&&f.children,D=f?f.shapeFlag:0,T=d.children,{patchFlag:L,shapeFlag:j}=d;if(L>0){if(L&128){at(S,T,g,_,x,b,R,C,E);return}else if(L&256){Qe(S,T,g,_,x,b,R,C,E);return}}j&8?(D&16&&Me(S,x,b),T!==S&&a(g,T)):D&16?j&16?at(S,T,g,_,x,b,R,C,E):Me(S,x,b,!0):(D&8&&a(g,""),j&16&&Le(T,g,_,x,b,R,C,E))},Qe=(f,d,g,_,x,b,R,C,E)=>{f=f||Ot,d=d||Ot;const S=f.length,D=d.length,T=Math.min(S,D);let L;for(L=0;L<T;L++){const j=d[L]=E?dt(d[L]):We(d[L]);O(f[L],j,g,null,x,b,R,C,E)}S>D?Me(f,x,b,!0,!1,T):Le(d,g,_,x,b,R,C,E,T)},at=(f,d,g,_,x,b,R,C,E)=>{let S=0;const D=d.length;let T=f.length-1,L=D-1;for(;S<=T&&S<=L;){const j=f[S],U=d[S]=E?dt(d[S]):We(d[S]);if(Wt(j,U))O(j,U,g,null,x,b,R,C,E);else break;S++}for(;S<=T&&S<=L;){const j=f[T],U=d[L]=E?dt(d[L]):We(d[L]);if(Wt(j,U))O(j,U,g,null,x,b,R,C,E);else break;T--,L--}if(S>T){if(S<=L){const j=L+1,U=j<D?d[j].el:_;for(;S<=L;)O(null,d[S]=E?dt(d[S]):We(d[S]),g,U,x,b,R,C,E),S++}}else if(S>L)for(;S<=T;)be(f[S],x,b,!0),S++;else{const j=S,U=S,Z=new Map;for(S=U;S<=L;S++){const Ce=d[S]=E?dt(d[S]):We(d[S]);Ce.key!=null&&Z.set(Ce.key,S)}let Y,Ee=0;const xe=L-U+1;let je=!1,Ve=0;const Ut=new Array(xe);for(S=0;S<xe;S++)Ut[S]=0;for(S=j;S<=T;S++){const Ce=f[S];if(Ee>=xe){be(Ce,x,b,!0);continue}let Be;if(Ce.key!=null)Be=Z.get(Ce.key);else for(Y=U;Y<=L;Y++)if(Ut[Y-U]===0&&Wt(Ce,d[Y])){Be=Y;break}Be===void 0?be(Ce,x,b,!0):(Ut[Be-U]=S+1,Be>=Ve?Ve=Be:je=!0,O(Ce,d[Be],g,null,x,b,R,C,E),Ee++)}const Us=je?Pl(Ut):Ot;for(Y=Us.length-1,S=xe-1;S>=0;S--){const Ce=U+S,Be=d[Ce],Ks=Ce+1<D?d[Ce+1].el:_;Ut[S]===0?O(null,Be,g,Ks,x,b,R,C,E):je&&(Y<0||S!==Us[Y]?Ne(Be,g,Ks,2):Y--)}}},Ne=(f,d,g,_,x=null)=>{const{el:b,type:R,transition:C,children:E,shapeFlag:S}=f;if(S&6){Ne(f.component.subTree,d,g,_);return}if(S&128){f.suspense.move(d,g,_);return}if(S&64){R.move(f,d,g,k);return}if(R===Pe){s(b,d,g);for(let T=0;T<E.length;T++)Ne(E[T],d,g,_);s(f.anchor,d,g);return}if(R===En){F(f,d,g);return}if(_!==2&&S&1&&C)if(_===0)C.beforeEnter(b),s(b,d,g),Re(()=>C.enter(b),x);else{const{leave:T,delayLeave:L,afterLeave:j}=C,U=()=>{f.ctx.isUnmounted?i(b):s(b,d,g)},Z=()=>{T(b,()=>{U(),j&&j()})};L?L(b,U,Z):Z()}else s(b,d,g)},be=(f,d,g,_=!1,x=!1)=>{const{type:b,props:R,ref:C,children:E,dynamicChildren:S,shapeFlag:D,patchFlag:T,dirs:L,cacheIndex:j}=f;if(T===-2&&(x=!1),C!=null&&(st(),In(C,null,g,f,!0),it()),j!=null&&(d.renderCache[j]=void 0),D&256){d.ctx.deactivate(f);return}const U=D&1&&L,Z=!Jt(f);let Y;if(Z&&(Y=R&&R.onVnodeBeforeUnmount)&&Ue(Y,d,f),D&6)gn(f.component,g,_);else{if(D&128){f.suspense.unmount(g,_);return}U&&yt(f,null,d,"beforeUnmount"),D&64?f.type.remove(f,d,g,k,_):S&&!S.hasOnce&&(b!==Pe||T>0&&T&64)?Me(S,d,g,!1,!0):(b===Pe&&T&384||!x&&D&16)&&Me(E,d,g),_&&Rt(f)}(Z&&(Y=R&&R.onVnodeUnmounted)||U)&&Re(()=>{Y&&Ue(Y,d,f),U&&yt(f,null,d,"unmounted")},g)},Rt=f=>{const{type:d,el:g,anchor:_,transition:x}=f;if(d===Pe){Pt(g,_);return}if(d===En){I(f);return}const b=()=>{i(g),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(f.shapeFlag&1&&x&&!x.persisted){const{leave:R,delayLeave:C}=x,E=()=>R(g,b);C?C(f.el,b,E):E()}else b()},Pt=(f,d)=>{let g;for(;f!==d;)g=p(f),i(f),f=g;i(d)},gn=(f,d,g)=>{const{bum:_,scope:x,job:b,subTree:R,um:C,m:E,a:S,parent:D,slots:{__:T}}=f;ti(E),ti(S),_&&bn(_),D&&H(T)&&T.forEach(L=>{D.renderCache[L]=void 0}),x.stop(),b&&(b.flags|=8,be(R,f,d,g)),C&&Re(C,d),Re(()=>{f.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Me=(f,d,g,_=!1,x=!1,b=0)=>{for(let R=b;R<f.length;R++)be(f[R],d,g,_,x)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const d=p(f.anchor||f.el),g=d&&d[Qr];return g?p(g):d};let $=!1;const A=(f,d,g)=>{f==null?d._vnode&&be(d._vnode,null,null,!0):O(d._vnode||null,f,d,null,null,null,g),d._vnode=f,$||($=!0,Ys(),_o(),$=!1)},k={p:O,um:be,m:Ne,r:Rt,mt:Bt,mc:Le,pc:W,pbc:De,n:y,o:e};return{render:A,hydrate:void 0,createApp:vl(A)}}function ss({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function bt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Rl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ko(e,t,n=!1){const s=e.children,i=t.children;if(H(s)&&H(i))for(let o=0;o<s.length;o++){const r=s[o];let l=i[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[o]=dt(i[o]),l.el=r.el),!n&&l.patchFlag!==-2&&ko(r,l)),l.type===Gn&&(l.el=r.el),l.type===vt&&!l.el&&(l.el=r.el)}}function Pl(e){const t=e.slice(),n=[0];let s,i,o,r,l;const c=e.length;for(s=0;s<c;s++){const u=e[s];if(u!==0){if(i=n[n.length-1],e[i]<u){t[s]=i,n.push(s);continue}for(o=0,r=n.length-1;o<r;)l=o+r>>1,e[n[l]]<u?o=l+1:r=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,r=n[o-1];o-- >0;)n[o]=r,r=t[r];return n}function Lo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Lo(t)}function ti(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Al=Symbol.for("v-scx"),Tl=()=>nt(Al);function Sn(e,t,n){return Do(e,t,n)}function Do(e,t,n=X){const{immediate:s,deep:i,flush:o,once:r}=n,l=me({},n),c=t&&s||!t&&o!=="post";let u;if(cn){if(o==="sync"){const m=Tl();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=ze,m.resume=ze,m.pause=ze,m}}const a=ye;l.call=(m,P,O)=>Ye(m,a,P,O);let h=!1;o==="post"?l.scheduler=m=>{Re(m,a&&a.suspense)}:o!=="sync"&&(h=!0,l.scheduler=(m,P)=>{P?m():ks(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const p=zr(e,t,l);return cn&&(u?u.push(p):c&&p()),p}function Ml(e,t,n){const s=this.proxy,i=ie(e)?e.includes(".")?Ho(s,e):()=>s[e]:e.bind(s,s);let o;V(t)?o=t:(o=t.handler,n=t);const r=pn(this),l=Do(i,o.bind(s),n);return r(),l}function Ho(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const Ol=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${gt(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function Il(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||X;let i=n;const o=t.startsWith("update:"),r=o&&Ol(s,t.slice(7));r&&(r.trim&&(i=n.map(a=>ie(a)?a.trim():a)),r.number&&(i=n.map(Pn)));let l,c=s[l=Jn(t)]||s[l=Jn(gt(t))];!c&&o&&(c=s[l=Jn(Et(t))]),c&&Ye(c,e,6,i);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ye(u,e,6,i)}}function No(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const o=e.emits;let r={},l=!1;if(!V(e)){const c=u=>{const a=No(u,t,!0);a&&(l=!0,me(r,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(te(e)&&s.set(e,null),null):(H(o)?o.forEach(c=>r[c]=null):me(r,o),te(e)&&s.set(e,r),r)}function qn(e,t){return!e||!Hn(t)?!1:(t=t.slice(2).replace(/Once$/,""),G(e,t[0].toLowerCase()+t.slice(1))||G(e,Et(t))||G(e,t))}function ni(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[o],slots:r,attrs:l,emit:c,render:u,renderCache:a,props:h,data:p,setupState:m,ctx:P,inheritAttrs:O}=e,N=On(e);let w,M;try{if(n.shapeFlag&4){const I=i||s,K=I;w=We(u.call(K,I,a,h,m,p,P)),M=l}else{const I=t;w=We(I.length>1?I(h,{attrs:l,slots:r,emit:c}):I(h,null)),M=t.props?l:$l(l)}}catch(I){Zt.length=0,Wn(I,e,1),w=B(vt)}let F=w;if(M&&O!==!1){const I=Object.keys(M),{shapeFlag:K}=F;I.length&&K&7&&(o&&I.some(Es)&&(M=Fl(M,o)),F=Dt(F,M,!1,!0))}return n.dirs&&(F=Dt(F,null,!1,!0),F.dirs=F.dirs?F.dirs.concat(n.dirs):n.dirs),n.transition&&Ls(F,n.transition),w=F,On(N),w}const $l=e=>{let t;for(const n in e)(n==="class"||n==="style"||Hn(n))&&((t||(t={}))[n]=e[n]);return t},Fl=(e,t)=>{const n={};for(const s in e)(!Es(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function kl(e,t,n){const{props:s,children:i,component:o}=e,{props:r,children:l,patchFlag:c}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?si(s,r,u):!!r;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const p=a[h];if(r[p]!==s[p]&&!qn(u,p))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:s===r?!1:s?r?si(s,r,u):!0:!!r;return!1}function si(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const o=s[i];if(t[o]!==e[o]&&!qn(n,o))return!0}return!1}function Ll({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const jo=e=>e.__isSuspense;function Dl(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Yr(e)}const Pe=Symbol.for("v-fgt"),Gn=Symbol.for("v-txt"),vt=Symbol.for("v-cmt"),En=Symbol.for("v-stc"),Zt=[];let Te=null;function ae(e=!1){Zt.push(Te=e?null:[])}function Hl(){Zt.pop(),Te=Zt[Zt.length-1]||null}let rn=1;function ii(e,t=!1){rn+=e,e<0&&Te&&t&&(Te.hasOnce=!0)}function Vo(e){return e.dynamicChildren=rn>0?Te||Ot:null,Hl(),rn>0&&Te&&Te.push(e),e}function de(e,t,n,s,i,o){return Vo(v(e,t,n,s,i,o,!0))}function Nl(e,t,n,s,i){return Vo(B(e,t,n,s,i,!0))}function Fn(e){return e?e.__v_isVNode===!0:!1}function Wt(e,t){return e.type===t.type&&e.key===t.key}const Bo=({key:e})=>e??null,Cn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ie(e)||ge(e)||V(e)?{i:Oe,r:e,k:t,f:!!n}:e:null);function v(e,t=null,n=null,s=0,i=null,o=e===Pe?0:1,r=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Bo(t),ref:t&&Cn(t),scopeId:bo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Oe};return l?(Ns(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ie(n)?8:16),rn>0&&!r&&Te&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Te.push(c),c}const B=jl;function jl(e,t=null,n=null,s=0,i=null,o=!1){if((!e||e===al)&&(e=vt),Fn(e)){const l=Dt(e,t,!0);return n&&Ns(l,n),rn>0&&!o&&Te&&(l.shapeFlag&6?Te[Te.indexOf(e)]=l:Te.push(l)),l.patchFlag=-2,l}if(Jl(e)&&(e=e.__vccOpts),t){t=Vl(t);let{class:l,style:c}=t;l&&!ie(l)&&(t.class=le(l)),te(c)&&($s(c)&&!H(c)&&(c=me({},c)),t.style=dn(c))}const r=ie(e)?1:jo(e)?128:Jr(e)?64:te(e)?4:V(e)?2:0;return v(e,t,n,s,i,r,o,!0)}function Vl(e){return e?$s(e)||To(e)?me({},e):e:null}function Dt(e,t,n=!1,s=!1){const{props:i,ref:o,patchFlag:r,children:l,transition:c}=e,u=t?Ul(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Bo(u),ref:t&&t.ref?n&&o?H(o)?o.concat(Cn(t)):[o,Cn(t)]:Cn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Pe?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Dt(e.ssContent),ssFallback:e.ssFallback&&Dt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ls(a,c.clone(a)),a}function ce(e=" ",t=0){return B(Gn,null,e,t)}function ln(e,t){const n=B(En,null,e);return n.staticCount=t,n}function Bl(e="",t=!1){return t?(ae(),Nl(vt,null,e)):B(vt,null,e)}function We(e){return e==null||typeof e=="boolean"?B(vt):H(e)?B(Pe,null,e.slice()):Fn(e)?dt(e):B(Gn,null,String(e))}function dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Dt(e)}function Ns(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),Ns(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!To(t)?t._ctx=Oe:i===3&&Oe&&(Oe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else V(t)?(t={default:t,_ctx:Oe},n=32):(t=String(t),s&64?(n=16,t=[ce(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ul(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=le([t.class,s.class]));else if(i==="style")t.style=dn([t.style,s.style]);else if(Hn(i)){const o=t[i],r=s[i];r&&o!==r&&!(H(o)&&o.includes(r))&&(t[i]=o?[].concat(o,r):r)}else i!==""&&(t[i]=s[i])}return t}function Ue(e,t,n,s=null){Ye(e,t,7,[n,s])}const Kl=Ro();let Wl=0;function zl(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Kl,o={uid:Wl++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Oo(s,i),emitsOptions:No(s,i),emit:null,emitted:null,propsDefaults:X,inheritAttrs:s.inheritAttrs,ctx:X,data:X,props:X,attrs:X,slots:X,refs:X,setupState:X,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Il.bind(null,o),e.ce&&e.ce(o),o}let ye=null,kn,_s;{const e=Vn(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),o=>{i.length>1?i.forEach(r=>r(o)):i[0](o)}};kn=t("__VUE_INSTANCE_SETTERS__",n=>ye=n),_s=t("__VUE_SSR_SETTERS__",n=>cn=n)}const pn=e=>{const t=ye;return kn(e),e.scope.on(),()=>{e.scope.off(),kn(t)}},oi=()=>{ye&&ye.scope.off(),kn(null)};function Uo(e){return e.vnode.shapeFlag&4}let cn=!1;function ql(e,t=!1,n=!1){t&&_s(t);const{props:s,children:i}=e.vnode,o=Uo(e);_l(e,s,o,t),wl(e,i,n||t);const r=o?Gl(e,t):void 0;return t&&_s(!1),r}function Gl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ul);const{setup:s}=n;if(s){st();const i=e.setupContext=s.length>1?Ql(e):null,o=pn(e),r=hn(s,e,0,[e.props,i]),l=Vi(r);if(it(),o(),(l||e.sp)&&!Jt(e)&&xo(e),l){if(r.then(oi,oi),t)return r.then(c=>{ri(e,c)}).catch(c=>{Wn(c,e,0)});e.asyncDep=r}else ri(e,r)}else Ko(e)}function ri(e,t,n){V(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:te(t)&&(e.setupState=go(t)),Ko(e)}function Ko(e,t,n){const s=e.type;e.render||(e.render=s.render||ze);{const i=pn(e);st();try{fl(e)}finally{it(),i()}}}const Yl={get(e,t){return pe(e,"get",""),e[t]}};function Ql(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Yl),slots:e.slots,emit:e.emit,expose:t}}function Yn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(go(ho(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Xt)return Xt[n](e)},has(t,n){return n in t||n in Xt}})):e.proxy}function Jl(e){return V(e)&&"__vccOpts"in e}const Ae=(e,t)=>Kr(e,t,cn);function Wo(e,t,n){const s=arguments.length;return s===2?te(t)&&!H(t)?Fn(t)?B(e,null,[t]):B(e,t):B(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Fn(n)&&(n=[n]),B(e,t,n))}const Xl="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ys;const li=typeof window<"u"&&window.trustedTypes;if(li)try{ys=li.createPolicy("vue",{createHTML:e=>e})}catch{}const zo=ys?e=>ys.createHTML(e):e=>e,Zl="http://www.w3.org/2000/svg",ec="http://www.w3.org/1998/Math/MathML",Ze=typeof document<"u"?document:null,ci=Ze&&Ze.createElement("template"),tc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?Ze.createElementNS(Zl,e):t==="mathml"?Ze.createElementNS(ec,e):n?Ze.createElement(e,{is:n}):Ze.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>Ze.createTextNode(e),createComment:e=>Ze.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ze.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,o){const r=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{ci.innerHTML=zo(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ci.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},nc=Symbol("_vtc");function sc(e,t,n){const s=e[nc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ai=Symbol("_vod"),ic=Symbol("_vsh"),oc=Symbol(""),rc=/(^|;)\s*display\s*:/;function lc(e,t,n){const s=e.style,i=ie(n);let o=!1;if(n&&!i){if(t)if(ie(t))for(const r of t.split(";")){const l=r.slice(0,r.indexOf(":")).trim();n[l]==null&&Rn(s,l,"")}else for(const r in t)n[r]==null&&Rn(s,r,"");for(const r in n)r==="display"&&(o=!0),Rn(s,r,n[r])}else if(i){if(t!==n){const r=s[oc];r&&(n+=";"+r),s.cssText=n,o=rc.test(n)}}else t&&e.removeAttribute("style");ai in e&&(e[ai]=o?s.display:"",e[ic]&&(s.display="none"))}const ui=/\s*!important$/;function Rn(e,t,n){if(H(n))n.forEach(s=>Rn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=cc(e,t);ui.test(n)?e.setProperty(Et(s),n.replace(ui,""),"important"):e[s]=n}}const fi=["Webkit","Moz","ms"],is={};function cc(e,t){const n=is[t];if(n)return n;let s=gt(t);if(s!=="filter"&&s in e)return is[t]=s;s=Ki(s);for(let i=0;i<fi.length;i++){const o=fi[i]+s;if(o in e)return is[t]=o}return t}const di="http://www.w3.org/1999/xlink";function hi(e,t,n,s,i,o=vr(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(di,t.slice(6,t.length)):e.setAttributeNS(di,t,n):n==null||o&&!zi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":qe(n)?String(n):n)}function pi(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?zo(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let r=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=zi(n):n==null&&l==="string"?(n="",r=!0):l==="number"&&(n=0,r=!0)}try{e[t]=n}catch{}r&&e.removeAttribute(i||t)}function wt(e,t,n,s){e.addEventListener(t,n,s)}function ac(e,t,n,s){e.removeEventListener(t,n,s)}const gi=Symbol("_vei");function uc(e,t,n,s,i=null){const o=e[gi]||(e[gi]={}),r=o[t];if(s&&r)r.value=s;else{const[l,c]=fc(t);if(s){const u=o[t]=pc(s,i);wt(e,l,u,c)}else r&&(ac(e,l,r,c),o[t]=void 0)}}const mi=/(?:Once|Passive|Capture)$/;function fc(e){let t;if(mi.test(e)){t={};let s;for(;s=e.match(mi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let os=0;const dc=Promise.resolve(),hc=()=>os||(dc.then(()=>os=0),os=Date.now());function pc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ye(gc(s,n.value),t,5,[s])};return n.value=e,n.attached=hc(),n}function gc(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const vi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,mc=(e,t,n,s,i,o)=>{const r=i==="svg";t==="class"?sc(e,s,r):t==="style"?lc(e,n,s):Hn(t)?Es(t)||uc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):vc(e,t,s,r))?(pi(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&hi(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ie(s))?pi(e,gt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),hi(e,t,s,r))};function vc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&vi(t)&&V(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return vi(t)&&ie(n)?!1:t in e}const Ln=e=>{const t=e.props["onUpdate:modelValue"]||!1;return H(t)?n=>bn(t,n):t};function _c(e){e.target.composing=!0}function _i(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Lt=Symbol("_assign"),of={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[Lt]=Ln(i);const o=s||i.props&&i.props.type==="number";wt(e,t?"change":"input",r=>{if(r.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=Pn(l)),e[Lt](l)}),n&&wt(e,"change",()=>{e.value=e.value.trim()}),t||(wt(e,"compositionstart",_c),wt(e,"compositionend",_i),wt(e,"change",_i))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:o}},r){if(e[Lt]=Ln(r),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Pn(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===c)||(e.value=c))}},rf={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=Nn(t);wt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>n?Pn(Dn(r)):Dn(r));e[Lt](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,Fs(()=>{e._assigning=!1})}),e[Lt]=Ln(s)},mounted(e,{value:t}){yi(e,t)},beforeUpdate(e,t,n){e[Lt]=Ln(n)},updated(e,{value:t}){e._assigning||yi(e,t)}};function yi(e,t){const n=e.multiple,s=H(t);if(!(n&&!s&&!Nn(t))){for(let i=0,o=e.options.length;i<o;i++){const r=e.options[i],l=Dn(r);if(n)if(s){const c=typeof l;c==="string"||c==="number"?r.selected=t.some(u=>String(u)===String(l)):r.selected=yr(t,l)>-1}else r.selected=t.has(l);else if(Bn(Dn(r),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Dn(e){return"_value"in e?e._value:e.value}const yc=["ctrl","shift","alt","meta"],bc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>yc.some(n=>e[`${n}Key`]&&!t.includes(n))},lf=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(i,...o)=>{for(let r=0;r<t.length;r++){const l=bc[t[r]];if(l&&l(i,t))return}return e(i,...o)})},xc=me({patchProp:mc},tc);let bi;function wc(){return bi||(bi=El(xc))}const Sc=(...e)=>{const t=wc().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=Cc(s);if(!i)return;const o=t._component;!V(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const r=n(i,!1,Ec(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),r},t};function Ec(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Cc(e){return ie(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const Rc=Symbol();var xi;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(xi||(xi={}));function Pc(){const e=br(!0),t=e.run(()=>Ge({}));let n=[],s=[];const i=ho({install(o){i._a=o,o.provide(Rc,i),o.config.globalProperties.$pinia=i,s.forEach(r=>n.push(r)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Mt=typeof document<"u";function qo(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ac(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&qo(e.default)}const z=Object.assign;function rs(e,t){const n={};for(const s in t){const i=t[s];n[s]=Fe(i)?i.map(e):e(i)}return n}const en=()=>{},Fe=Array.isArray,Go=/#/g,Tc=/&/g,Mc=/\//g,Oc=/=/g,Ic=/\?/g,Yo=/\+/g,$c=/%5B/g,Fc=/%5D/g,Qo=/%5E/g,kc=/%60/g,Jo=/%7B/g,Lc=/%7C/g,Xo=/%7D/g,Dc=/%20/g;function js(e){return encodeURI(""+e).replace(Lc,"|").replace($c,"[").replace(Fc,"]")}function Hc(e){return js(e).replace(Jo,"{").replace(Xo,"}").replace(Qo,"^")}function bs(e){return js(e).replace(Yo,"%2B").replace(Dc,"+").replace(Go,"%23").replace(Tc,"%26").replace(kc,"`").replace(Jo,"{").replace(Xo,"}").replace(Qo,"^")}function Nc(e){return bs(e).replace(Oc,"%3D")}function jc(e){return js(e).replace(Go,"%23").replace(Ic,"%3F")}function Vc(e){return e==null?"":jc(e).replace(Mc,"%2F")}function an(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Bc=/\/$/,Uc=e=>e.replace(Bc,"");function ls(e,t,n="/"){let s,i={},o="",r="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),i=e(o)),l>-1&&(s=s||t.slice(0,l),r=t.slice(l,t.length)),s=qc(s??t,n),{fullPath:s+(o&&"?")+o+r,path:s,query:i,hash:an(r)}}function Kc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function wi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Wc(e,t,n){const s=t.matched.length-1,i=n.matched.length-1;return s>-1&&s===i&&Ht(t.matched[s],n.matched[i])&&Zo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ht(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Zo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!zc(e[n],t[n]))return!1;return!0}function zc(e,t){return Fe(e)?Si(e,t):Fe(t)?Si(t,e):e===t}function Si(e,t){return Fe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function qc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),i=s[s.length-1];(i===".."||i===".")&&s.push("");let o=n.length-1,r,l;for(r=0;r<s.length;r++)if(l=s[r],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(r).join("/")}const ut={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var un;(function(e){e.pop="pop",e.push="push"})(un||(un={}));var tn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(tn||(tn={}));function Gc(e){if(!e)if(Mt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Uc(e)}const Yc=/^[^#]+#/;function Qc(e,t){return e.replace(Yc,"#")+t}function Jc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Qn=()=>({left:window.scrollX,top:window.scrollY});function Xc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=Jc(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ei(e,t){return(history.state?history.state.position-t:-1)+e}const xs=new Map;function Zc(e,t){xs.set(e,t)}function ea(e){const t=xs.get(e);return xs.delete(e),t}let ta=()=>location.protocol+"//"+location.host;function er(e,t){const{pathname:n,search:s,hash:i}=t,o=e.indexOf("#");if(o>-1){let l=i.includes(e.slice(o))?e.slice(o).length:1,c=i.slice(l);return c[0]!=="/"&&(c="/"+c),wi(c,"")}return wi(n,e)+s+i}function na(e,t,n,s){let i=[],o=[],r=null;const l=({state:p})=>{const m=er(e,location),P=n.value,O=t.value;let N=0;if(p){if(n.value=m,t.value=p,r&&r===P){r=null;return}N=O?p.position-O.position:0}else s(m);i.forEach(w=>{w(n.value,P,{delta:N,type:un.pop,direction:N?N>0?tn.forward:tn.back:tn.unknown})})};function c(){r=n.value}function u(p){i.push(p);const m=()=>{const P=i.indexOf(p);P>-1&&i.splice(P,1)};return o.push(m),m}function a(){const{history:p}=window;p.state&&p.replaceState(z({},p.state,{scroll:Qn()}),"")}function h(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:u,destroy:h}}function Ci(e,t,n,s=!1,i=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:i?Qn():null}}function sa(e){const{history:t,location:n}=window,s={value:er(e,n)},i={value:t.state};i.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,u,a){const h=e.indexOf("#"),p=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:ta()+e+c;try{t[a?"replaceState":"pushState"](u,"",p),i.value=u}catch(m){console.error(m),n[a?"replace":"assign"](p)}}function r(c,u){const a=z({},t.state,Ci(i.value.back,c,i.value.forward,!0),u,{position:i.value.position});o(c,a,!0),s.value=c}function l(c,u){const a=z({},i.value,t.state,{forward:c,scroll:Qn()});o(a.current,a,!0);const h=z({},Ci(s.value,c,null),{position:a.position+1},u);o(c,h,!1),s.value=c}return{location:s,state:i,push:l,replace:r}}function ia(e){e=Gc(e);const t=sa(e),n=na(e,t.state,t.location,t.replace);function s(o,r=!0){r||n.pauseListeners(),history.go(o)}const i=z({location:"",base:e,go:s,createHref:Qc.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function oa(e){return typeof e=="string"||e&&typeof e=="object"}function tr(e){return typeof e=="string"||typeof e=="symbol"}const nr=Symbol("");var Ri;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ri||(Ri={}));function Nt(e,t){return z(new Error,{type:e,[nr]:!0},t)}function Xe(e,t){return e instanceof Error&&nr in e&&(t==null||!!(e.type&t))}const Pi="[^/]+?",ra={sensitive:!1,strict:!1,start:!0,end:!0},la=/[.+*?^${}()[\]/\\]/g;function ca(e,t){const n=z({},ra,t),s=[];let i=n.start?"^":"";const o=[];for(const u of e){const a=u.length?[]:[90];n.strict&&!u.length&&(i+="/");for(let h=0;h<u.length;h++){const p=u[h];let m=40+(n.sensitive?.25:0);if(p.type===0)h||(i+="/"),i+=p.value.replace(la,"\\$&"),m+=40;else if(p.type===1){const{value:P,repeatable:O,optional:N,regexp:w}=p;o.push({name:P,repeatable:O,optional:N});const M=w||Pi;if(M!==Pi){m+=10;try{new RegExp(`(${M})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${P}" (${M}): `+I.message)}}let F=O?`((?:${M})(?:/(?:${M}))*)`:`(${M})`;h||(F=N&&u.length<2?`(?:/${F})`:"/"+F),N&&(F+="?"),i+=F,m+=20,N&&(m+=-8),O&&(m+=-20),M===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const r=new RegExp(i,n.sensitive?"":"i");function l(u){const a=u.match(r),h={};if(!a)return null;for(let p=1;p<a.length;p++){const m=a[p]||"",P=o[p-1];h[P.name]=m&&P.repeatable?m.split("/"):m}return h}function c(u){let a="",h=!1;for(const p of e){(!h||!a.endsWith("/"))&&(a+="/"),h=!1;for(const m of p)if(m.type===0)a+=m.value;else if(m.type===1){const{value:P,repeatable:O,optional:N}=m,w=P in u?u[P]:"";if(Fe(w)&&!O)throw new Error(`Provided param "${P}" is an array but it is not repeatable (* or + modifiers)`);const M=Fe(w)?w.join("/"):w;if(!M)if(N)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):h=!0);else throw new Error(`Missing required param "${P}"`);a+=M}}return a||"/"}return{re:r,score:s,keys:o,parse:l,stringify:c}}function aa(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function sr(e,t){let n=0;const s=e.score,i=t.score;for(;n<s.length&&n<i.length;){const o=aa(s[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-s.length)===1){if(Ai(s))return 1;if(Ai(i))return-1}return i.length-s.length}function Ai(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ua={type:0,value:""},fa=/[a-zA-Z0-9_]/;function da(e){if(!e)return[[]];if(e==="/")return[[ua]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,s=n;const i=[];let o;function r(){o&&i.push(o),o=[]}let l=0,c,u="",a="";function h(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(u&&h(),r()):c===":"?(h(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:fa.test(c)?p():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),h(),r(),i}function ha(e,t,n){const s=ca(da(e.path),n),i=z(s,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function pa(e,t){const n=[],s=new Map;t=Ii({strict:!1,end:!0,sensitive:!1},t);function i(h){return s.get(h)}function o(h,p,m){const P=!m,O=Mi(h);O.aliasOf=m&&m.record;const N=Ii(t,h),w=[O];if("alias"in h){const I=typeof h.alias=="string"?[h.alias]:h.alias;for(const K of I)w.push(Mi(z({},O,{components:m?m.record.components:O.components,path:K,aliasOf:m?m.record:O})))}let M,F;for(const I of w){const{path:K}=I;if(p&&K[0]!=="/"){const oe=p.record.path,ne=oe[oe.length-1]==="/"?"":"/";I.path=p.record.path+(K&&ne+K)}if(M=ha(I,p,N),m?m.alias.push(M):(F=F||M,F!==M&&F.alias.push(M),P&&h.name&&!Oi(M)&&r(h.name)),ir(M)&&c(M),O.children){const oe=O.children;for(let ne=0;ne<oe.length;ne++)o(oe[ne],M,m&&m.children[ne])}m=m||M}return F?()=>{r(F)}:en}function r(h){if(tr(h)){const p=s.get(h);p&&(s.delete(h),n.splice(n.indexOf(p),1),p.children.forEach(r),p.alias.forEach(r))}else{const p=n.indexOf(h);p>-1&&(n.splice(p,1),h.record.name&&s.delete(h.record.name),h.children.forEach(r),h.alias.forEach(r))}}function l(){return n}function c(h){const p=va(h,n);n.splice(p,0,h),h.record.name&&!Oi(h)&&s.set(h.record.name,h)}function u(h,p){let m,P={},O,N;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw Nt(1,{location:h});N=m.record.name,P=z(Ti(p.params,m.keys.filter(F=>!F.optional).concat(m.parent?m.parent.keys.filter(F=>F.optional):[]).map(F=>F.name)),h.params&&Ti(h.params,m.keys.map(F=>F.name))),O=m.stringify(P)}else if(h.path!=null)O=h.path,m=n.find(F=>F.re.test(O)),m&&(P=m.parse(O),N=m.record.name);else{if(m=p.name?s.get(p.name):n.find(F=>F.re.test(p.path)),!m)throw Nt(1,{location:h,currentLocation:p});N=m.record.name,P=z({},p.params,h.params),O=m.stringify(P)}const w=[];let M=m;for(;M;)w.unshift(M.record),M=M.parent;return{name:N,path:O,params:P,matched:w,meta:ma(w)}}e.forEach(h=>o(h));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:r,clearRoutes:a,getRoutes:l,getRecordMatcher:i}}function Ti(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Mi(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ga(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ga(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Oi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ma(e){return e.reduce((t,n)=>z(t,n.meta),{})}function Ii(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function va(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;sr(e,t[o])<0?s=o:n=o+1}const i=_a(e);return i&&(s=t.lastIndexOf(i,s-1)),s}function _a(e){let t=e;for(;t=t.parent;)if(ir(t)&&sr(e,t)===0)return t}function ir({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ya(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<s.length;++i){const o=s[i].replace(Yo," "),r=o.indexOf("="),l=an(r<0?o:o.slice(0,r)),c=r<0?null:an(o.slice(r+1));if(l in t){let u=t[l];Fe(u)||(u=t[l]=[u]),u.push(c)}else t[l]=c}return t}function $i(e){let t="";for(let n in e){const s=e[n];if(n=Nc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Fe(s)?s.map(o=>o&&bs(o)):[s&&bs(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function ba(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Fe(s)?s.map(i=>i==null?null:""+i):s==null?s:""+s)}return t}const xa=Symbol(""),Fi=Symbol(""),Vs=Symbol(""),or=Symbol(""),ws=Symbol("");function zt(){let e=[];function t(s){return e.push(s),()=>{const i=e.indexOf(s);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ht(e,t,n,s,i,o=r=>r()){const r=s&&(s.enterCallbacks[i]=s.enterCallbacks[i]||[]);return()=>new Promise((l,c)=>{const u=p=>{p===!1?c(Nt(4,{from:n,to:t})):p instanceof Error?c(p):oa(p)?c(Nt(2,{from:t,to:p})):(r&&s.enterCallbacks[i]===r&&typeof p=="function"&&r.push(p),l())},a=o(()=>e.call(s&&s.instances[i],t,n,u));let h=Promise.resolve(a);e.length<3&&(h=h.then(u)),h.catch(p=>c(p))})}function cs(e,t,n,s,i=o=>o()){const o=[];for(const r of e)for(const l in r.components){let c=r.components[l];if(!(t!=="beforeRouteEnter"&&!r.instances[l]))if(qo(c)){const a=(c.__vccOpts||c)[t];a&&o.push(ht(a,n,s,r,l,i))}else{let u=c();o.push(()=>u.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${r.path}"`);const h=Ac(a)?a.default:a;r.mods[l]=a,r.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&ht(m,n,s,r,l,i)()}))}}return o}function ki(e){const t=nt(Vs),n=nt(or),s=Ae(()=>{const c=se(e.to);return t.resolve(c)}),i=Ae(()=>{const{matched:c}=s.value,{length:u}=c,a=c[u-1],h=n.matched;if(!a||!h.length)return-1;const p=h.findIndex(Ht.bind(null,a));if(p>-1)return p;const m=Li(c[u-2]);return u>1&&Li(a)===m&&h[h.length-1].path!==m?h.findIndex(Ht.bind(null,c[u-2])):p}),o=Ae(()=>i.value>-1&&Ca(n.params,s.value.params)),r=Ae(()=>i.value>-1&&i.value===n.matched.length-1&&Zo(n.params,s.value.params));function l(c={}){if(Ea(c)){const u=t[se(e.replace)?"replace":"push"](se(e.to)).catch(en);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:Ae(()=>s.value.href),isActive:o,isExactActive:r,navigate:l}}function wa(e){return e.length===1?e[0]:e}const Sa=ke({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ki,setup(e,{slots:t}){const n=Kn(ki(e)),{options:s}=nt(Vs),i=Ae(()=>({[Di(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Di(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&wa(t.default(n));return e.custom?o:Wo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),he=Sa;function Ea(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ca(e,t){for(const n in t){const s=t[n],i=e[n];if(typeof s=="string"){if(s!==i)return!1}else if(!Fe(i)||i.length!==s.length||s.some((o,r)=>o!==i[r]))return!1}return!0}function Li(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Di=(e,t,n)=>e??t??n,Ra=ke({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=nt(ws),i=Ae(()=>e.route||s.value),o=nt(Fi,0),r=Ae(()=>{let u=se(o);const{matched:a}=i.value;let h;for(;(h=a[u])&&!h.components;)u++;return u}),l=Ae(()=>i.value.matched[r.value]);wn(Fi,Ae(()=>r.value+1)),wn(xa,l),wn(ws,i);const c=Ge();return Sn(()=>[c.value,l.value,e.name],([u,a,h],[p,m,P])=>{a&&(a.instances[h]=u,m&&m!==a&&u&&u===p&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),u&&a&&(!m||!Ht(a,m)||!p)&&(a.enterCallbacks[h]||[]).forEach(O=>O(u))},{flush:"post"}),()=>{const u=i.value,a=e.name,h=l.value,p=h&&h.components[a];if(!p)return Hi(n.default,{Component:p,route:u});const m=h.props[a],P=m?m===!0?u.params:typeof m=="function"?m(u):m:null,N=Wo(p,z({},P,t,{onVnodeUnmounted:w=>{w.component.isUnmounted&&(h.instances[a]=null)},ref:c}));return Hi(n.default,{Component:N,route:u})||N}}});function Hi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const rr=Ra;function Pa(e){const t=pa(e.routes,e),n=e.parseQuery||ya,s=e.stringifyQuery||$i,i=e.history,o=zt(),r=zt(),l=zt(),c=jr(ut);let u=ut;Mt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=rs.bind(null,y=>""+y),h=rs.bind(null,Vc),p=rs.bind(null,an);function m(y,$){let A,k;return tr(y)?(A=t.getRecordMatcher(y),k=$):k=y,t.addRoute(k,A)}function P(y){const $=t.getRecordMatcher(y);$&&t.removeRoute($)}function O(){return t.getRoutes().map(y=>y.record)}function N(y){return!!t.getRecordMatcher(y)}function w(y,$){if($=z({},$||c.value),typeof y=="string"){const g=ls(n,y,$.path),_=t.resolve({path:g.path},$),x=i.createHref(g.fullPath);return z(g,_,{params:p(_.params),hash:an(g.hash),redirectedFrom:void 0,href:x})}let A;if(y.path!=null)A=z({},y,{path:ls(n,y.path,$.path).path});else{const g=z({},y.params);for(const _ in g)g[_]==null&&delete g[_];A=z({},y,{params:h(g)}),$.params=h($.params)}const k=t.resolve(A,$),J=y.hash||"";k.params=a(p(k.params));const f=Kc(s,z({},y,{hash:Hc(J),path:k.path})),d=i.createHref(f);return z({fullPath:f,hash:J,query:s===$i?ba(y.query):y.query||{}},k,{redirectedFrom:void 0,href:d})}function M(y){return typeof y=="string"?ls(n,y,c.value.path):z({},y)}function F(y,$){if(u!==y)return Nt(8,{from:$,to:y})}function I(y){return ne(y)}function K(y){return I(z(M(y),{replace:!0}))}function oe(y){const $=y.matched[y.matched.length-1];if($&&$.redirect){const{redirect:A}=$;let k=typeof A=="function"?A(y):A;return typeof k=="string"&&(k=k.includes("?")||k.includes("#")?k=M(k):{path:k},k.params={}),z({query:y.query,hash:y.hash,params:k.path!=null?{}:y.params},k)}}function ne(y,$){const A=u=w(y),k=c.value,J=y.state,f=y.force,d=y.replace===!0,g=oe(A);if(g)return ne(z(M(g),{state:typeof g=="object"?z({},J,g.state):J,force:f,replace:d}),$||A);const _=A;_.redirectedFrom=$;let x;return!f&&Wc(s,k,A)&&(x=Nt(16,{to:_,from:k}),Ne(k,k,!0,!1)),(x?Promise.resolve(x):De(_,k)).catch(b=>Xe(b)?Xe(b,2)?b:at(b):W(b,_,k)).then(b=>{if(b){if(Xe(b,2))return ne(z({replace:d},M(b.to),{state:typeof b.to=="object"?z({},J,b.to.state):J,force:f}),$||_)}else b=_t(_,k,!0,d,J);return ct(_,k,b),b})}function Le(y,$){const A=F(y,$);return A?Promise.reject(A):Promise.resolve()}function lt(y){const $=Pt.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(y):y()}function De(y,$){let A;const[k,J,f]=Aa(y,$);A=cs(k.reverse(),"beforeRouteLeave",y,$);for(const g of k)g.leaveGuards.forEach(_=>{A.push(ht(_,y,$))});const d=Le.bind(null,y,$);return A.push(d),Me(A).then(()=>{A=[];for(const g of o.list())A.push(ht(g,y,$));return A.push(d),Me(A)}).then(()=>{A=cs(J,"beforeRouteUpdate",y,$);for(const g of J)g.updateGuards.forEach(_=>{A.push(ht(_,y,$))});return A.push(d),Me(A)}).then(()=>{A=[];for(const g of f)if(g.beforeEnter)if(Fe(g.beforeEnter))for(const _ of g.beforeEnter)A.push(ht(_,y,$));else A.push(ht(g.beforeEnter,y,$));return A.push(d),Me(A)}).then(()=>(y.matched.forEach(g=>g.enterCallbacks={}),A=cs(f,"beforeRouteEnter",y,$,lt),A.push(d),Me(A))).then(()=>{A=[];for(const g of r.list())A.push(ht(g,y,$));return A.push(d),Me(A)}).catch(g=>Xe(g,8)?g:Promise.reject(g))}function ct(y,$,A){l.list().forEach(k=>lt(()=>k(y,$,A)))}function _t(y,$,A,k,J){const f=F(y,$);if(f)return f;const d=$===ut,g=Mt?history.state:{};A&&(k||d?i.replace(y.fullPath,z({scroll:d&&g&&g.scroll},J)):i.push(y.fullPath,J)),c.value=y,Ne(y,$,A,d),at()}let He;function Bt(){He||(He=i.listen((y,$,A)=>{if(!gn.listening)return;const k=w(y),J=oe(k);if(J){ne(z(J,{replace:!0,force:!0}),k).catch(en);return}u=k;const f=c.value;Mt&&Zc(Ei(f.fullPath,A.delta),Qn()),De(k,f).catch(d=>Xe(d,12)?d:Xe(d,2)?(ne(z(M(d.to),{force:!0}),k).then(g=>{Xe(g,20)&&!A.delta&&A.type===un.pop&&i.go(-1,!1)}).catch(en),Promise.reject()):(A.delta&&i.go(-A.delta,!1),W(d,k,f))).then(d=>{d=d||_t(k,f,!1),d&&(A.delta&&!Xe(d,8)?i.go(-A.delta,!1):A.type===un.pop&&Xe(d,20)&&i.go(-1,!1)),ct(k,f,d)}).catch(en)}))}let Ct=zt(),re=zt(),Q;function W(y,$,A){at(y);const k=re.list();return k.length?k.forEach(J=>J(y,$,A)):console.error(y),Promise.reject(y)}function Qe(){return Q&&c.value!==ut?Promise.resolve():new Promise((y,$)=>{Ct.add([y,$])})}function at(y){return Q||(Q=!y,Bt(),Ct.list().forEach(([$,A])=>y?A(y):$()),Ct.reset()),y}function Ne(y,$,A,k){const{scrollBehavior:J}=e;if(!Mt||!J)return Promise.resolve();const f=!A&&ea(Ei(y.fullPath,0))||(k||!A)&&history.state&&history.state.scroll||null;return Fs().then(()=>J(y,$,f)).then(d=>d&&Xc(d)).catch(d=>W(d,y,$))}const be=y=>i.go(y);let Rt;const Pt=new Set,gn={currentRoute:c,listening:!0,addRoute:m,removeRoute:P,clearRoutes:t.clearRoutes,hasRoute:N,getRoutes:O,resolve:w,options:e,push:I,replace:K,go:be,back:()=>be(-1),forward:()=>be(1),beforeEach:o.add,beforeResolve:r.add,afterEach:l.add,onError:re.add,isReady:Qe,install(y){const $=this;y.component("RouterLink",he),y.component("RouterView",rr),y.config.globalProperties.$router=$,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>se(c)}),Mt&&!Rt&&c.value===ut&&(Rt=!0,I(i.location).catch(J=>{}));const A={};for(const J in ut)Object.defineProperty(A,J,{get:()=>c.value[J],enumerable:!0});y.provide(Vs,$),y.provide(or,uo(A)),y.provide(ws,c);const k=y.unmount;Pt.add(y),y.unmount=function(){Pt.delete(y),Pt.size<1&&(u=ut,He&&He(),He=null,c.value=ut,Rt=!1,Q=!1),k()}}};function Me(y){return y.reduce(($,A)=>$.then(()=>lt(A)),Promise.resolve())}return gn}function Aa(e,t){const n=[],s=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let r=0;r<o;r++){const l=t.matched[r];l&&(e.matched.find(u=>Ht(u,l))?s.push(l):n.push(l));const c=e.matched[r];c&&(t.matched.find(u=>Ht(u,c))||i.push(c))}return[n,s,i]}const Ta={class:"nav container"},Ma={class:"nav__logo"},Oa={class:"nav__item"},Ia={class:"nav__item"},$a={class:"nav__item"},Fa={class:"nav__item"},ka={class:"nav__item"},La={class:"nav__cta"},Da=["aria-expanded"],Ha=ke({__name:"AppHeader",setup(e){const t=Ge(!1),n=Ge(!1),s=()=>{t.value=window.scrollY>50},i=()=>{n.value=!n.value},o=()=>{n.value=!1};return jt(()=>{window.addEventListener("scroll",s)}),Vt(()=>{window.removeEventListener("scroll",s)}),(r,l)=>(ae(),de("header",{class:le(["header",{"header--scrolled":t.value}])},[v("nav",Ta,[v("div",Ma,[B(se(he),{to:"/",class:"logo-link"},{default:fe(()=>l[0]||(l[0]=[v("span",{class:"logo-text"},"IoT",-1),v("span",{class:"logo-accent"},"Vision",-1)])),_:1,__:[0]})]),v("ul",{class:le(["nav__menu",{"nav__menu--open":n.value}])},[v("li",Oa,[B(se(he),{to:"/",class:"nav__link",onClick:o},{default:fe(()=>l[1]||(l[1]=[ce(" Home ")])),_:1,__:[1]})]),v("li",Ia,[B(se(he),{to:"/solutions",class:"nav__link",onClick:o},{default:fe(()=>l[2]||(l[2]=[ce(" Solutions ")])),_:1,__:[2]})]),v("li",$a,[B(se(he),{to:"/technologies",class:"nav__link",onClick:o},{default:fe(()=>l[3]||(l[3]=[ce(" Technologies ")])),_:1,__:[3]})]),v("li",Fa,[B(se(he),{to:"/about",class:"nav__link",onClick:o},{default:fe(()=>l[4]||(l[4]=[ce(" About ")])),_:1,__:[4]})]),v("li",ka,[B(se(he),{to:"/contact",class:"nav__link",onClick:o},{default:fe(()=>l[5]||(l[5]=[ce(" Contact ")])),_:1,__:[5]})])],2),v("div",La,[B(se(he),{to:"/contact",class:"btn btn-primary"},{default:fe(()=>l[6]||(l[6]=[ce(" Get Started ")])),_:1,__:[6]})]),v("button",{class:"nav__toggle",onClick:i,"aria-expanded":n.value,"aria-label":"Toggle navigation menu"},l[7]||(l[7]=[v("span",{class:"nav__toggle-line"},null,-1),v("span",{class:"nav__toggle-line"},null,-1),v("span",{class:"nav__toggle-line"},null,-1)]),8,Da)])],2))}}),rt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,i]of t)n[s]=i;return n},Na=rt(Ha,[["__scopeId","data-v-8a26f02c"]]),ja={class:"footer"},Va={class:"container"},Ba={class:"footer__content"},Ua={class:"footer__links"},Ka={class:"footer__list"},Wa={class:"footer__bottom"},za={class:"footer__copyright"},qa=ke({__name:"AppFooter",setup(e){const t=Ae(()=>new Date().getFullYear());return(n,s)=>(ae(),de("footer",ja,[v("div",Va,[v("div",Ba,[s[6]||(s[6]=ln('<div class="footer__brand" data-v-7c145b91><div class="footer__logo" data-v-7c145b91><span class="logo-text" data-v-7c145b91>IoT</span><span class="logo-accent" data-v-7c145b91>Vision</span></div><p class="footer__description" data-v-7c145b91> Transforming the future with cutting-edge IoT solutions. Where innovation meets intelligence. </p><div class="footer__social" data-v-7c145b91><a href="#" class="social-link" aria-label="LinkedIn" data-v-7c145b91><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-7c145b91><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" data-v-7c145b91></path></svg></a><a href="#" class="social-link" aria-label="Twitter" data-v-7c145b91><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-7c145b91><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" data-v-7c145b91></path></svg></a><a href="#" class="social-link" aria-label="GitHub" data-v-7c145b91><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-7c145b91><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" data-v-7c145b91></path></svg></a></div></div>',1)),v("div",Ua,[s[5]||(s[5]=v("h4",{class:"footer__title"},"Quick Links",-1)),v("ul",Ka,[v("li",null,[B(se(he),{to:"/",class:"footer__link"},{default:fe(()=>s[0]||(s[0]=[ce("Home")])),_:1,__:[0]})]),v("li",null,[B(se(he),{to:"/solutions",class:"footer__link"},{default:fe(()=>s[1]||(s[1]=[ce("Solutions")])),_:1,__:[1]})]),v("li",null,[B(se(he),{to:"/technologies",class:"footer__link"},{default:fe(()=>s[2]||(s[2]=[ce("Technologies")])),_:1,__:[2]})]),v("li",null,[B(se(he),{to:"/about",class:"footer__link"},{default:fe(()=>s[3]||(s[3]=[ce("About")])),_:1,__:[3]})]),v("li",null,[B(se(he),{to:"/contact",class:"footer__link"},{default:fe(()=>s[4]||(s[4]=[ce("Contact")])),_:1,__:[4]})])])]),s[7]||(s[7]=ln('<div class="footer__links" data-v-7c145b91><h4 class="footer__title" data-v-7c145b91>Services</h4><ul class="footer__list" data-v-7c145b91><li data-v-7c145b91><a href="#" class="footer__link" data-v-7c145b91>IoT Development</a></li><li data-v-7c145b91><a href="#" class="footer__link" data-v-7c145b91>Smart Home Solutions</a></li><li data-v-7c145b91><a href="#" class="footer__link" data-v-7c145b91>Hardware Design</a></li><li data-v-7c145b91><a href="#" class="footer__link" data-v-7c145b91>Cloud Integration</a></li><li data-v-7c145b91><a href="#" class="footer__link" data-v-7c145b91>Consulting</a></li></ul></div><div class="footer__contact" data-v-7c145b91><h4 class="footer__title" data-v-7c145b91>Get in Touch</h4><div class="contact-item" data-v-7c145b91><span class="contact-label" data-v-7c145b91>Email:</span><a href="mailto:<EMAIL>" class="contact-value" data-v-7c145b91><EMAIL></a></div><div class="contact-item" data-v-7c145b91><span class="contact-label" data-v-7c145b91>Phone:</span><a href="tel:+420123456789" class="contact-value" data-v-7c145b91>+420 123 456 789</a></div><div class="contact-item" data-v-7c145b91><span class="contact-label" data-v-7c145b91>Address:</span><span class="contact-value" data-v-7c145b91>Prague, Czech Republic</span></div></div>',2))]),v("div",Wa,[v("div",za,[v("p",null,"© "+Se(t.value)+" IoTVision. All rights reserved.",1)]),s[8]||(s[8]=v("div",{class:"footer__legal"},[v("a",{href:"#",class:"footer__link"},"Privacy Policy"),v("a",{href:"#",class:"footer__link"},"Terms of Service")],-1))])])]))}}),Ga=rt(qa,[["__scopeId","data-v-7c145b91"]]),Ya={class:"particle-background"},Qa=80,Ni=150,as=100,Ja=ke({__name:"ParticleBackground",setup(e){const t=Ge(null);let n=null,s=[];const i=["#00FFFF","#00FF7F","#FF00FF"];let o={x:0,y:0},r,l;const c=w=>({x:Math.random()*r.width,y:Math.random()*r.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:Math.random()*2+1,opacity:Math.random()*.5+.3,color:i[Math.floor(Math.random()*i.length)],connections:[]}),u=()=>{s=[];for(let w=0;w<Qa;w++)s.push(c())},a=w=>{w.x+=w.vx,w.y+=w.vy;const M=o.x-w.x,F=o.y-w.y,I=Math.sqrt(M*M+F*F);if(I<as){const K=(as-I)/as;w.vx+=M/I*K*.01,w.vy+=F/I*K*.01}(w.x<0||w.x>r.width)&&(w.vx*=-1,w.x=Math.max(0,Math.min(r.width,w.x))),(w.y<0||w.y>r.height)&&(w.vy*=-1,w.y=Math.max(0,Math.min(r.height,w.y))),w.vx*=.99,w.vy*=.99,w.opacity+=(Math.random()-.5)*.01,w.opacity=Math.max(.1,Math.min(.8,w.opacity))},h=w=>{l.save(),l.globalAlpha=w.opacity,l.fillStyle=w.color,l.shadowBlur=10,l.shadowColor=w.color,l.beginPath(),l.arc(w.x,w.y,w.size,0,Math.PI*2),l.fill(),l.restore()},p=()=>{for(let w=0;w<s.length;w++)for(let M=w+1;M<s.length;M++){const F=s[w].x-s[M].x,I=s[w].y-s[M].y,K=Math.sqrt(F*F+I*I);if(K<Ni){const oe=(1-K/Ni)*.3;l.save(),l.globalAlpha=oe,l.strokeStyle=s[w].color,l.lineWidth=.5,l.shadowBlur=5,l.shadowColor=s[w].color,l.beginPath(),l.moveTo(s[w].x,s[w].y),l.lineTo(s[M].x,s[M].y),l.stroke(),l.restore()}}},m=()=>{l.clearRect(0,0,r.width,r.height),s.forEach(w=>{a(w),h(w)}),p(),n=requestAnimationFrame(m)},P=()=>{r&&(r.width=window.innerWidth,r.height=window.innerHeight,u())},O=w=>{o.x=w.clientX,o.y=w.clientY},N=()=>{P()};return jt(()=>{r=t.value,l=r.getContext("2d"),P(),u(),m(),window.addEventListener("resize",N),window.addEventListener("mousemove",O)}),Vt(()=>{n&&cancelAnimationFrame(n),window.removeEventListener("resize",N),window.removeEventListener("mousemove",O)}),(w,M)=>(ae(),de("div",Ya,[v("canvas",{ref_key:"canvasRef",ref:t,class:"particle-canvas"},null,512)]))}}),Xa=rt(Ja,[["__scopeId","data-v-765ad019"]]),Za={id:"app",class:"app"},eu={class:"main-content"},tu=ke({__name:"App",setup(e){return(t,n)=>(ae(),de("div",Za,[B(Xa),B(Na),v("main",eu,[B(se(rr))]),B(Ga)]))}}),nu=rt(tu,[["__scopeId","data-v-e8c19943"]]),su="modulepreload",iu=function(e){return"/"+e},ji={},yn=function(t,n,s){let i=Promise.resolve();if(n&&n.length>0){let r=function(u){return Promise.all(u.map(a=>Promise.resolve(a).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),c=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));i=r(n.map(u=>{if(u=iu(u),u in ji)return;ji[u]=!0;const a=u.endsWith(".css"),h=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${h}`))return;const p=document.createElement("link");if(p.rel=a?"stylesheet":su,a||(p.as="script"),p.crossOrigin="",p.href=u,c&&p.setAttribute("nonce",c),document.head.appendChild(p),a)return new Promise((m,P)=>{p.addEventListener("load",m),p.addEventListener("error",()=>P(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(r){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=r,window.dispatchEvent(l),!l.defaultPrevented)throw r}return i.then(r=>{for(const l of r||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})},ou={class:"hero"},ru={class:"hero__background"},lu={class:"container hero__container"},cu={class:"hero__content"},au={class:"visual-container"},uu={class:"floating-elements"},fu={class:"central-hub"},du=ke({__name:"HeroSection",setup(e){const t=Ge(null),n=Ge(!1);let s=null,i,o;const r=()=>{console.log("Playing demo...")},l=()=>{if(!t.value)return;i=t.value,o=i.getContext("2d");const c=()=>{i.width=i.offsetWidth,i.height=i.offsetHeight};c(),window.addEventListener("resize",c);const u=[],a=()=>({x:-20,y:Math.random()*i.height,vx:1+Math.random()*2,vy:(Math.random()-.5)*.5,opacity:.3+Math.random()*.4});for(let p=0;p<5;p++)u.push(a());const h=()=>{o.clearRect(0,0,i.width,i.height),u.forEach((p,m)=>{if(p.x+=p.vx,p.y+=p.vy,p.x>i.width+20){u[m]=a();return}o.save(),o.globalAlpha=p.opacity,o.fillStyle="#00FFFF",o.shadowBlur=10,o.shadowColor="#00FFFF",o.beginPath(),o.arc(p.x,p.y,2,0,Math.PI*2),o.fill(),o.strokeStyle="#00FFFF",o.lineWidth=1,o.beginPath(),o.moveTo(p.x-20,p.y),o.lineTo(p.x,p.y),o.stroke(),o.restore()}),s=requestAnimationFrame(h)};h()};return jt(()=>{setTimeout(()=>{n.value=!0},100),setTimeout(()=>{l()},500)}),Vt(()=>{s&&cancelAnimationFrame(s)}),(c,u)=>(ae(),de("section",ou,[v("div",ru,[v("canvas",{ref_key:"heroCanvasRef",ref:t,class:"hero__canvas"},null,512)]),v("div",lu,[v("div",cu,[v("div",{class:le(["hero__badge",{"animate-fade-in":n.value}])},u[0]||(u[0]=[v("span",{class:"badge-text"},"Next Generation IoT",-1)]),2),v("h1",{class:le(["hero__title",{"animate-slide-in-up delay-200":n.value}])},u[1]||(u[1]=[ce(" Welcome to the "),v("span",{class:"title-highlight"},"Future",-1),ce(" of Connected Intelligence ")]),2),v("p",{class:le(["hero__subtitle",{"animate-slide-in-up delay-300":n.value}])}," Transform your world with cutting-edge IoT solutions that bridge the gap between imagination and reality. Where every device becomes intelligent, every connection meaningful, and every data point a step toward tomorrow. ",2),v("div",{class:le(["hero__actions",{"animate-slide-in-up delay-500":n.value}])},[B(se(he),{to:"/contact",class:"btn btn-primary hero__cta"},{default:fe(()=>u[2]||(u[2]=[v("span",null,"Explore Solutions",-1),v("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[v("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),_:1,__:[2]}),v("button",{class:"btn btn-ghost hero__demo",onClick:r},u[3]||(u[3]=[v("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[v("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})],-1),v("span",null,"Watch Demo",-1)]))],2),v("div",{class:le(["hero__stats",{"animate-fade-in delay-700":n.value}])},u[4]||(u[4]=[ln('<div class="stat" data-v-4c973816><div class="stat-number" data-v-4c973816>500+</div><div class="stat-label" data-v-4c973816>Projects Delivered</div></div><div class="stat" data-v-4c973816><div class="stat-number" data-v-4c973816>50M+</div><div class="stat-label" data-v-4c973816>Devices Connected</div></div><div class="stat" data-v-4c973816><div class="stat-number" data-v-4c973816>99.9%</div><div class="stat-label" data-v-4c973816>Uptime Guaranteed</div></div>',3)]),2)]),v("div",{class:le(["hero__visual",{"animate-scale-in delay-400":n.value}])},[v("div",au,[v("div",uu,[v("div",{class:le(["element element--1",{"animate-float":n.value}])},null,2),v("div",{class:le(["element element--2",{"animate-float delay-100":n.value}])},null,2),v("div",{class:le(["element element--3",{"animate-float delay-200":n.value}])},null,2)]),v("div",fu,[v("div",{class:le(["hub-core",{"animate-pulse-neon":n.value}])},null,2),u[5]||(u[5]=v("div",{class:"hub-rings"},[v("div",{class:"ring ring--1"}),v("div",{class:"ring ring--2"}),v("div",{class:"ring ring--3"})],-1))])])],2)]),v("div",{class:le(["hero__scroll-indicator",{"animate-fade-in delay-1000":n.value}])},u[6]||(u[6]=[v("div",{class:"scroll-text"},"Scroll to explore",-1),v("div",{class:"scroll-arrow"},null,-1)]),2)]))}}),hu=rt(du,[["__scopeId","data-v-4c973816"]]),pu={class:"features section"},gu={class:"container"},mu={class:"features__grid"},vu={class:"feature-card__icon"},_u={class:"icon-placeholder"},yu={class:"feature-card__title"},bu={class:"feature-card__description"},xu={class:"feature-card__stats"},wu={class:"stat"},Su={class:"stat-value"},Eu={class:"stat-label"},Cu=ke({__name:"FeaturesSection",setup(e){const t=Ge(!1),n=[{id:1,title:"Ultra-Fast Connectivity",description:"Lightning-fast data transmission with 99.9% uptime guarantee. Our advanced protocols ensure your devices stay connected.",iconText:"⚡",stat:{value:"<1ms",label:"Latency"}},{id:2,title:"AI-Powered Analytics",description:"Transform raw data into actionable insights with our machine learning algorithms and predictive analytics.",iconText:"🧠",stat:{value:"95%",label:"Accuracy"}},{id:3,title:"Enterprise Security",description:"Bank-grade encryption and multi-layer security protocols protect your data and devices from threats.",iconText:"🛡️",stat:{value:"256-bit",label:"Encryption"}},{id:4,title:"Scalable Infrastructure",description:"From prototype to production, our platform scales seamlessly to support millions of connected devices.",iconText:"📈",stat:{value:"10M+",label:"Devices"}},{id:5,title:"Real-time Monitoring",description:"Monitor your entire IoT ecosystem in real-time with our intuitive dashboard and alert system.",iconText:"📊",stat:{value:"24/7",label:"Monitoring"}},{id:6,title:"Edge Computing",description:"Process data at the edge for reduced latency and improved performance with our distributed computing.",iconText:"🔗",stat:{value:"50%",label:"Faster"}}];return jt(()=>{const s=new IntersectionObserver(o=>{o.forEach(r=>{r.isIntersecting&&(t.value=!0)})},{threshold:.1}),i=document.querySelector(".features");i&&s.observe(i)}),(s,i)=>(ae(),de("section",pu,[v("div",gu,[i[0]||(i[0]=v("div",{class:"features__header"},[v("h2",{class:"features__title"},[ce(" Why Choose "),v("span",{class:"text-gradient"},"IoTVision")]),v("p",{class:"features__subtitle"}," Experience the next generation of IoT solutions designed for the future ")],-1)),v("div",mu,[(ae(),de(Pe,null,xn(n,(o,r)=>v("div",{key:o.id,class:le(["feature-card",{"animate-scale-in":t.value}]),style:dn({animationDelay:`${r*.1}s`})},[v("div",vu,[v("div",_u,Se(o.iconText),1)]),v("h3",yu,Se(o.title),1),v("p",bu,Se(o.description),1),v("div",xu,[v("div",wu,[v("span",Su,Se(o.stat.value),1),v("span",Eu,Se(o.stat.label),1)])])],6)),64))])])]))}}),Ru=rt(Cu,[["__scopeId","data-v-1ab9d325"]]),Pu={class:"technologies-preview section"},Au={class:"container"},Tu={class:"technologies__content"},Mu={class:"tech-showcase"},Ou={class:"tech-visual"},Iu={class:"tech-nodes"},$u=["onClick"],Fu={class:"node-icon"},ku={class:"node-label"},Lu={class:"tech-details"},Du={key:0,class:"tech-card"},Hu={class:"tech-card__title"},Nu={class:"tech-card__description"},ju={class:"tech-specs"},Vu={class:"spec-label"},Bu={class:"spec-value"},Uu={class:"tech-features"},Ku={class:"features-list"},Wu=ke({__name:"TechnologiesPreview",setup(e){const t=Ge(null),n=Ge(0),s=[{id:1,x:20,y:30,icon:"🔗",label:"Connectivity"},{id:2,x:50,y:20,icon:"🧠",label:"AI/ML"},{id:3,x:80,y:35,icon:"☁️",label:"Cloud"},{id:4,x:30,y:70,icon:"🔒",label:"Security"},{id:5,x:70,y:75,icon:"📊",label:"Analytics"}],i=[{title:"Advanced Connectivity",description:"Multi-protocol support including WiFi 6, 5G, LoRaWAN, and Bluetooth 5.0 for seamless device communication.",specs:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km+"},{label:"Latency",value:"<1ms"}],features:["Auto-discovery and pairing","Mesh networking capabilities","Adaptive protocol switching","Edge-to-cloud connectivity"]},{title:"AI & Machine Learning",description:"Intelligent data processing with real-time analytics, predictive maintenance, and automated decision making.",specs:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:["Predictive analytics","Anomaly detection","Natural language processing","Computer vision integration"]},{title:"Cloud Infrastructure",description:"Scalable cloud platform with global edge computing, automatic scaling, and enterprise-grade reliability.",specs:[{label:"Uptime",value:"99.99%"},{label:"Regions",value:"25+"},{label:"Scale",value:"Unlimited"}],features:["Auto-scaling infrastructure","Global edge deployment","Multi-cloud support","Disaster recovery"]},{title:"Enterprise Security",description:"End-to-end encryption, zero-trust architecture, and compliance with international security standards.",specs:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2, ISO27001"},{label:"Authentication",value:"Multi-factor"}],features:["Zero-trust architecture","Blockchain verification","Threat intelligence","Automated security updates"]},{title:"Advanced Analytics",description:"Real-time data visualization, custom dashboards, and business intelligence tools for actionable insights.",specs:[{label:"Data Points",value:"1B+/day"},{label:"Visualization",value:"Real-time"},{label:"APIs",value:"RESTful"}],features:["Custom dashboards","Real-time alerts","Data export tools","Business intelligence"]}],o=Ae(()=>i[n.value]),r=u=>{n.value=u};let l=null;const c=()=>{if(!t.value)return;const u=t.value,a=u.getContext("2d"),h=()=>{u.width=u.offsetWidth,u.height=u.offsetHeight};h(),window.addEventListener("resize",h);const p=()=>{a.clearRect(0,0,u.width,u.height),a.strokeStyle="rgba(0, 255, 255, 0.3)",a.lineWidth=1;for(let m=0;m<s.length;m++)for(let P=m+1;P<s.length;P++){const O=s[m],N=s[P],w=O.x/100*u.width,M=O.y/100*u.height,F=N.x/100*u.width,I=N.y/100*u.height;a.beginPath(),a.moveTo(w,M),a.lineTo(F,I),a.stroke()}l=requestAnimationFrame(p)};p()};return jt(()=>{setTimeout(()=>{c()},500);const u=setInterval(()=>{n.value=(n.value+1)%i.length},5e3);Vt(()=>{clearInterval(u),l&&cancelAnimationFrame(l)})}),(u,a)=>(ae(),de("section",Pu,[v("div",Au,[a[2]||(a[2]=v("div",{class:"technologies__header"},[v("h2",{class:"technologies__title"},[ce(" Cutting-Edge "),v("span",{class:"text-gradient"},"Technologies")]),v("p",{class:"technologies__subtitle"}," Powered by the latest innovations in IoT, AI, and cloud computing ")],-1)),v("div",Tu,[v("div",Mu,[v("div",Ou,[v("canvas",{ref_key:"techCanvasRef",ref:t,class:"tech-canvas"},null,512),v("div",Iu,[(ae(),de(Pe,null,xn(s,(h,p)=>v("div",{key:h.id,class:le(["tech-node",{active:n.value===p}]),style:dn({left:h.x+"%",top:h.y+"%",animationDelay:`${p*.2}s`}),onClick:m=>r(p)},[v("div",Fu,Se(h.icon),1),v("div",ku,Se(h.label),1)],14,$u)),64))])]),v("div",Lu,[o.value?(ae(),de("div",Du,[v("h3",Hu,Se(o.value.title),1),v("p",Nu,Se(o.value.description),1),v("div",ju,[(ae(!0),de(Pe,null,xn(o.value.specs,h=>(ae(),de("div",{key:h.label,class:"spec-item"},[v("span",Vu,Se(h.label),1),v("span",Bu,Se(h.value),1)]))),128))]),v("div",Uu,[a[0]||(a[0]=v("h4",{class:"features-title"},"Key Features",-1)),v("ul",Ku,[(ae(!0),de(Pe,null,xn(o.value.features,h=>(ae(),de("li",{key:h},Se(h),1))),128))])])])):Bl("",!0)])]),a[1]||(a[1]=ln('<div class="tech-stats" data-v-d48f5229><div class="stat-card" data-v-d48f5229><div class="stat-number" data-v-d48f5229>15+</div><div class="stat-label" data-v-d48f5229>Technologies</div></div><div class="stat-card" data-v-d48f5229><div class="stat-number" data-v-d48f5229>99.9%</div><div class="stat-label" data-v-d48f5229>Reliability</div></div><div class="stat-card" data-v-d48f5229><div class="stat-number" data-v-d48f5229>24/7</div><div class="stat-label" data-v-d48f5229>Support</div></div></div>',1))])])]))}}),zu=rt(Wu,[["__scopeId","data-v-d48f5229"]]),qu={class:"cta-section section"},Gu={class:"container"},Yu={class:"cta-content"},Qu={class:"cta-actions"},Ju=ke({__name:"CTASection",setup(e){return(t,n)=>(ae(),de("section",qu,[v("div",Gu,[v("div",Yu,[n[2]||(n[2]=v("h2",{class:"cta-title"},[ce(" Ready to Transform Your "),v("span",{class:"text-gradient"},"Future"),ce("? ")],-1)),n[3]||(n[3]=v("p",{class:"cta-subtitle"}," Join thousands of companies already revolutionizing their operations with IoTVision. Start your journey into the connected future today. ",-1)),v("div",Qu,[B(se(he),{to:"/contact",class:"btn btn-primary cta-primary"},{default:fe(()=>n[0]||(n[0]=[v("span",null,"Get Started Now",-1),v("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[v("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),_:1,__:[0]}),B(se(he),{to:"/solutions",class:"btn btn-outline cta-secondary"},{default:fe(()=>n[1]||(n[1]=[v("span",null,"Explore Solutions",-1)])),_:1,__:[1]})]),n[4]||(n[4]=ln('<div class="cta-features" data-v-392037f3><div class="feature-item" data-v-392037f3><span class="feature-icon" data-v-392037f3>✓</span><span data-v-392037f3>Free Consultation</span></div><div class="feature-item" data-v-392037f3><span class="feature-icon" data-v-392037f3>✓</span><span data-v-392037f3>30-Day Trial</span></div><div class="feature-item" data-v-392037f3><span class="feature-icon" data-v-392037f3>✓</span><span data-v-392037f3>24/7 Support</span></div></div>',1))])])]))}}),Xu=rt(Ju,[["__scopeId","data-v-392037f3"]]),Zu={class:"home-view"},ef=ke({__name:"HomeView",setup(e){return(t,n)=>(ae(),de("div",Zu,[B(hu),B(Ru),B(zu),B(Xu)]))}}),tf=rt(ef,[["__scopeId","data-v-4b95c547"]]),nf=Pa({history:ia("/"),routes:[{path:"/",name:"home",component:tf},{path:"/solutions",name:"solutions",component:()=>yn(()=>import("./SolutionsView-CVdXMgtn.js"),__vite__mapDeps([0,1]))},{path:"/technologies",name:"technologies",component:()=>yn(()=>import("./TechnologiesView-dODy-tZv.js"),__vite__mapDeps([2,3]))},{path:"/about",name:"about",component:()=>yn(()=>import("./AboutView-BAYxmzcR.js"),__vite__mapDeps([4,5]))},{path:"/contact",name:"contact",component:()=>yn(()=>import("./ContactView-DZY8tii4.js"),__vite__mapDeps([6,7]))}],scrollBehavior(e,t,n){return n||{top:0}}}),Bs=Sc(nu);Bs.use(Pc());Bs.use(nf);Bs.mount("#app");export{Pe as F,he as R,rt as _,ln as a,v as b,de as c,ke as d,B as e,ce as f,Ge as g,Ae as h,Bl as i,Kn as j,lf as k,sf as l,rf as m,le as n,ae as o,xn as r,Se as t,se as u,of as v,fe as w};
