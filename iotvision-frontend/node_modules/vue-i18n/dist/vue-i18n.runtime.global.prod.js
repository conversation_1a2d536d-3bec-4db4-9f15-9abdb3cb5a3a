/*!
  * vue-i18n v9.14.4
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const a="undefined"!=typeof window,n=(e,t=!1)=>t?Symbol.for(e):Symbol(e),l=(e,t,a)=>r({l:e,k:t,s:a}),r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===T(e),i=e=>"[object RegExp]"===T(e),c=e=>F(e)&&0===Object.keys(e).length,u=Object.assign,m=Object.create,f=(e=null)=>m(e);function _(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const g=Object.prototype.hasOwnProperty;function p(e,t){return g.call(e,t)}const v=Array.isArray,d=e=>"function"==typeof e,b=e=>"string"==typeof e,E=e=>"boolean"==typeof e,h=e=>null!==e&&"object"==typeof e,k=e=>h(e)&&d(e.then)&&d(e.catch),L=Object.prototype.toString,T=e=>L.call(e),F=e=>{if(!h(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function y(e){let t=e;return()=>++t}function N(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const I=e=>!h(e)||v(e);function R(e,t){if(I(e)||I(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((n=>{"__proto__"!==n&&(h(e[n])&&!h(t[n])&&(t[n]=Array.isArray(e[n])?[]:f()),I(t[n])||I(e[n])?t[n]=e[n]:a.push({src:e[n],des:t[n]}))}))}}function O(e,t,a={}){const{domain:n,messages:l,args:r}=a,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}function M(e){return h(e)&&0===function(e,t,a){for(let n=0;n<t.length;n++){const a=t[n];if(p(e,a)&&null!=e[a])return e[a]}return a}(e,W)&&(p(e,"b")||p(e,"body"))}const W=["t","type"];const w=["b","body","c","cases","s","static","i","items","k","key","m","modifier","v","value",...W],P=[];P[0]={w:[0],i:[3,0],"[":[4],o:[7]},P[1]={w:[1],".":[2],"[":[4],o:[7]},P[2]={w:[2],i:[3,0],0:[3,0]},P[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},P[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},P[5]={"'":[4,0],o:8,l:[5,0]},P[6]={'"':[4,0],o:8,l:[6,0]};const C=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function D(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function A(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(a=t,C.test(a)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var a}const S=new Map;function $(e,t){return h(e)?e[t]:null}const U=e=>e,x=e=>"",H="text",j=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,a,n)=>0===n?e+a:e+t+a),"")}(e),V=e=>null==e?"":v(e)||F(e)&&e.toString===L?JSON.stringify(e,null,2):String(e);function G(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Y(e={}){const t=e.locale,a=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),n=h(e.pluralRules)&&b(t)&&d(e.pluralRules[t])?e.pluralRules[t]:G,l=h(e.pluralRules)&&b(t)&&d(e.pluralRules[t])?G:void 0,r=e.list||[],s=e.named||f();o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,s);function i(t){const a=d(e.messages)?e.messages(t):!!h(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):x)}const c=F(e.processor)&&d(e.processor.normalize)?e.processor.normalize:j,m=F(e.processor)&&d(e.processor.interpolate)?e.processor.interpolate:V,_={list:e=>r[e],named:e=>s[e],plural:e=>e[n(a,e.length,l)],linked:(t,...a)=>{const[n,l]=a;let r="text",o="";1===a.length?h(n)?(o=n.modifier||o,r=n.type||r):b(n)&&(o=n||o):2===a.length&&(b(n)&&(o=n||o),b(l)&&(r=l||r));const s=i(t)(_),c="vnode"===r&&v(s)&&o?s[0]:s;return o?(u=o,e.modifiers?e.modifiers[u]:U)(c,r):c;var u},message:i,type:F(e.processor)&&b(e.processor.type)?e.processor.type:H,interpolate:m,normalize:c,values:u(f(),r,s)};return _}const B=y(17),X={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:B(),INVALID_ISO_DATE_ARGUMENT:B(),NOT_SUPPORT_NON_STRING_MESSAGE:B(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:B(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:B(),NOT_SUPPORT_LOCALE_TYPE:B(),__EXTEND_POINT__:B()};function z(e,t){return null!=t.locale?q(t.locale):q(e.locale)}let J;function q(e){if(b(e))return e;if(d(e)){if(e.resolvedOnce&&null!=J)return J;if("Function"===e.constructor.name){const t=e();if(k(t))throw Error(X.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return J=t}throw Error(X.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(X.NOT_SUPPORT_LOCALE_TYPE)}function Z(e,t,a){return[...new Set([a,...v(t)?t:h(t)?Object.keys(t):b(t)?[t]:[a]])]}function K(e,t,a){const n=b(a)?a:le,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let r=l.__localeChainCache.get(n);if(!r){r=[];let e=[a];for(;v(e);)e=Q(r,e,t);const o=v(t)||!F(t)?t:t.default?t.default:null;e=b(o)?[o]:o,v(e)&&Q(r,e,!1),l.__localeChainCache.set(n,r)}return r}function Q(e,t,a){let n=!0;for(let l=0;l<t.length&&E(n);l++){const r=t[l];b(r)&&(n=ee(e,t[l],a))}return n}function ee(e,t,a){let n;const l=t.split("-");do{n=te(e,l.join("-"),a),l.splice(-1,1)}while(l.length&&!0===n);return n}function te(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const l=t.replace(/!/g,"");e.push(l),(v(a)||F(a))&&a[l]&&(n=a[l])}return n}const ae="9.14.4",ne=-1,le="en-US",re="",oe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let se,ie,ce;let ue=null;const me=e=>{ue=e},fe=()=>ue;let _e=0;function ge(e={}){const t=d(e.onWarn)?e.onWarn:N,a=b(e.version)?e.version:ae,n=b(e.locale)||d(e.locale)?e.locale:le,l=d(n)?le:n,r=v(e.fallbackLocale)||F(e.fallbackLocale)||b(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l,o=F(e.messages)?e.messages:pe(l),s=F(e.datetimeFormats)?e.datetimeFormats:pe(l),c=F(e.numberFormats)?e.numberFormats:pe(l),m=u(f(),e.modifiers,{upper:(e,t)=>"text"===t&&b(e)?e.toUpperCase():"vnode"===t&&h(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&b(e)?e.toLowerCase():"vnode"===t&&h(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&b(e)?oe(e):"vnode"===t&&h(e)&&"__v_isVNode"in e?oe(e.children):e}),_=e.pluralRules||f(),g=d(e.missing)?e.missing:null,p=!E(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,k=!E(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,L=!!e.fallbackFormat,T=!!e.unresolving,y=d(e.postTranslation)?e.postTranslation:null,I=F(e.processor)?e.processor:null,R=!E(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,M=d(e.messageCompiler)?e.messageCompiler:se,W=d(e.messageResolver)?e.messageResolver:ie||$,w=d(e.localeFallbacker)?e.localeFallbacker:ce||Z,P=h(e.fallbackContext)?e.fallbackContext:void 0,C=e,D=h(C.__datetimeFormatters)?C.__datetimeFormatters:new Map,A=h(C.__numberFormatters)?C.__numberFormatters:new Map,S=h(C.__meta)?C.__meta:{};_e++;const U={version:a,cid:_e,locale:n,fallbackLocale:r,messages:o,modifiers:m,pluralRules:_,missing:g,missingWarn:p,fallbackWarn:k,fallbackFormat:L,unresolving:T,postTranslation:y,processor:I,warnHtmlMessage:R,escapeParameter:O,messageCompiler:M,messageResolver:W,localeFallbacker:w,fallbackContext:P,onWarn:t,__meta:S};return U.datetimeFormats=s,U.numberFormats=c,U.__datetimeFormatters=D,U.__numberFormatters=A,U}const pe=e=>({[e]:f()});function ve(e,t,a,n,l){const{missing:r,onWarn:o}=e;if(null!==r){const n=r(e,a,t,l);return b(n)?n:t}return t}function de(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}function be(e,t){const a=t.indexOf(e);if(-1===a)return!1;for(let r=a+1;r<t.length;r++)if(n=e,l=t[r],n!==l&&n.split("-")[0]===l.split("-")[0])return!0;var n,l;return!1}const Ee=()=>"",he=e=>d(e);function ke(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:l,messageCompiler:r,fallbackLocale:s,messages:i}=e,[c,u]=Fe(...t),m=E(u.missingWarn)?u.missingWarn:e.missingWarn,g=E(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,p=E(u.escapeParameter)?u.escapeParameter:e.escapeParameter,d=!!u.resolvedMessage,k=b(u.default)||E(u.default)?E(u.default)?r?c:()=>c:u.default:a?r?c:()=>c:"",L=a||""!==k,T=z(e,u);p&&function(e){v(e.list)?e.list=e.list.map((e=>b(e)?_(e):e)):h(e.named)&&Object.keys(e.named).forEach((t=>{b(e.named[t])&&(e.named[t]=_(e.named[t]))}))}(u);let[F,y,N]=d?[c,T,i[T]||f()]:Le(e,c,T,s,g,m),I=F,R=c;if(d||b(I)||M(I)||he(I)||L&&(I=k,R=I),!(d||(b(I)||M(I)||he(I))&&b(y)))return l?ne:c;let O=!1;const W=he(I)?I:Te(e,c,y,I,R,(()=>{O=!0}));if(O)return I;const w=function(e,t,a,n){const{modifiers:l,pluralRules:r,messageResolver:s,fallbackLocale:i,fallbackWarn:c,missingWarn:u,fallbackContext:m}=e,f=n=>{let l=s(a,n);if(null==l&&m){const[,,e]=Le(m,n,t,i,c,u);l=s(e,n)}if(b(l)||M(l)){let a=!1;const r=Te(e,n,t,l,n,(()=>{a=!0}));return a?Ee:r}return he(l)?l:Ee},_={locale:t,modifiers:l,pluralRules:r,messages:f};e.processor&&(_.processor=e.processor);n.list&&(_.list=n.list);n.named&&(_.named=n.named);o(n.plural)&&(_.pluralIndex=n.plural);return _}(e,y,N,u),P=function(e,t,a){const n=t(a);return n}(0,W,Y(w));return n?n(P,c):P}function Le(e,t,a,n,l,r){const{messages:o,onWarn:s,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,a);let m,_=f(),g=null;for(let p=0;p<u.length&&(m=u[p],_=o[m]||f(),null===(g=i(_,t))&&(g=_[t]),!(b(g)||M(g)||he(g)));p++)if(!be(m,u)){const a=ve(e,t,m,0,"translate");a!==t&&(g=a)}return[g,m,_]}function Te(e,t,a,n,r,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(he(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==s){const e=()=>n;return e.locale=a,e.key=t,e}const c=s(n,function(e,t,a,n,r,o){return{locale:t,key:a,warnHtmlMessage:r,onError:e=>{throw o&&o(e),e},onCacheKey:e=>l(t,a,e)}}(0,a,r,0,i,o));return c.locale=a,c.key=t,c.source=n,c}function Fe(...e){const[t,a,n]=e,l=f();if(!(b(t)||o(t)||he(t)||M(t)))throw Error(X.INVALID_ARGUMENT);const r=o(t)?String(t):(he(t),t);return o(a)?l.plural=a:b(a)?l.default=a:F(a)&&!c(a)?l.named=a:v(a)&&(l.list=a),o(n)?l.plural=n:b(n)?l.default=n:F(n)&&u(l,n),[r,l]}function ye(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,m,f,_]=Ie(...t);E(f.missingWarn)?f.missingWarn:e.missingWarn;E(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=z(e,f),v=o(e,l,p);if(!b(i)||""===i)return new Intl.DateTimeFormat(p,_).format(m);let d,h={},k=null;for(let c=0;c<v.length&&(d=v[c],h=a[d]||{},k=h[i],!F(k));c++)ve(e,i,d,0,"datetime format");if(!F(k)||!b(d))return n?ne:i;let L=`${d}__${i}`;c(_)||(L=`${L}__${JSON.stringify(_)}`);let T=s.get(L);return T||(T=new Intl.DateTimeFormat(d,u({},k,_)),s.set(L,T)),g?T.formatToParts(m):T.format(m)}const Ne=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ie(...e){const[t,a,n,l]=e,r=f();let i,c=f();if(b(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(X.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(a);try{i.toISOString()}catch(u){throw Error(X.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(X.INVALID_DATE_ARGUMENT);i=t}else{if(!o(t))throw Error(X.INVALID_ARGUMENT);i=t}return b(a)?r.key=a:F(a)&&Object.keys(a).forEach((e=>{Ne.includes(e)?c[e]=a[e]:r[e]=a[e]})),b(n)?r.locale=n:F(n)&&(c=n),F(l)&&(c=l),[r.key||"",i,r,c]}function Re(e,t,a){const n=e;for(const l in a){const e=`${t}__${l}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function Oe(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,m,f,_]=We(...t);E(f.missingWarn)?f.missingWarn:e.missingWarn;E(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=z(e,f),v=o(e,l,p);if(!b(i)||""===i)return new Intl.NumberFormat(p,_).format(m);let d,h={},k=null;for(let c=0;c<v.length&&(d=v[c],h=a[d]||{},k=h[i],!F(k));c++)ve(e,i,d,0,"number format");if(!F(k)||!b(d))return n?ne:i;let L=`${d}__${i}`;c(_)||(L=`${L}__${JSON.stringify(_)}`);let T=s.get(L);return T||(T=new Intl.NumberFormat(d,u({},k,_)),s.set(L,T)),g?T.formatToParts(m):T.format(m)}const Me=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function We(...e){const[t,a,n,l]=e,r=f();let s=f();if(!o(t))throw Error(X.INVALID_ARGUMENT);const i=t;return b(a)?r.key=a:F(a)&&Object.keys(a).forEach((e=>{Me.includes(e)?s[e]=a[e]:r[e]=a[e]})),b(n)?r.locale=n:F(n)&&(s=n),F(l)&&(s=l),[r.key||"",i,r,s]}function we(e,t,a){const n=e;for(const l in a){const e=`${t}__${l}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const Pe="9.14.4",Ce=X.__EXTEND_POINT__,De=y(Ce),Ae={UNEXPECTED_RETURN_TYPE:Ce,INVALID_ARGUMENT:De(),MUST_BE_CALL_SETUP_TOP:De(),NOT_INSTALLED:De(),NOT_AVAILABLE_IN_LEGACY_MODE:De(),REQUIRED_VALUE:De(),INVALID_VALUE:De(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:De(),NOT_INSTALLED_WITH_PROVIDE:De(),UNEXPECTED_ERROR:De(),NOT_COMPATIBLE_LEGACY_VUE_I18N:De(),BRIDGE_SUPPORT_VUE_2_ONLY:De(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:De(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:De(),__EXTEND_POINT__:De()};const Se=n("__translateVNode"),$e=n("__datetimeParts"),Ue=n("__numberParts"),xe=n("__setPluralRules"),He=n("__injectWithOption"),je=n("__dispose");function Ve(e){if(!h(e))return e;if(M(e))return e;for(const t in e)if(p(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let l=e,r=!1;for(let e=0;e<n;e++){if("__proto__"===a[e])throw new Error(`unsafe key: ${a[e]}`);if(a[e]in l||(l[a[e]]=f()),!h(l[a[e]])){r=!0;break}l=l[a[e]]}if(r||(M(l)?w.includes(a[n])||delete e[t]:(l[a[n]]=e[t],delete e[t])),!M(l)){const e=l[a[n]];h(e)&&Ve(e)}}else h(e[t])&&Ve(e[t]);return e}function Ge(e,t){const{messages:a,__i18n:n,messageResolver:l,flatJson:r}=t,o=F(a)?a:v(n)?f():{[e]:f()};if(v(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||f(),R(a,o[t])):R(a,o)}else b(e)&&R(JSON.parse(e),o)})),null==l&&r)for(const s in o)p(o,s)&&Ve(o[s]);return o}function Ye(e){return e.type}function Be(e,t,a){let n=h(t.messages)?t.messages:f();"__i18nGlobal"in a&&(n=Ge(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const l=Object.keys(n);if(l.length&&l.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),h(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(h(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function Xe(e){return t.createVNode(t.Text,null,e,0)}const ze=()=>[],Je=()=>!1;let qe=0;function Ze(e){return(a,n,l,r)=>e(n,l,t.getCurrentInstance()||void 0,r)}function Ke(e={},n){const{__root:l,__injectWithOption:r}=e,s=void 0===l,c=e.flatJson,m=a?t.ref:t.shallowRef,f=!!e.translateExistCompatible;let _=!E(e.inheritLocale)||e.inheritLocale;const g=m(l&&_?l.locale.value:b(e.locale)?e.locale:le),k=m(l&&_?l.fallbackLocale.value:b(e.fallbackLocale)||v(e.fallbackLocale)||F(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:g.value),L=m(Ge(g.value,e)),T=m(F(e.datetimeFormats)?e.datetimeFormats:{[g.value]:{}}),y=m(F(e.numberFormats)?e.numberFormats:{[g.value]:{}});let N=l?l.missingWarn:!E(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,I=l?l.fallbackWarn:!E(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,O=l?l.fallbackRoot:!E(e.fallbackRoot)||e.fallbackRoot,W=!!e.fallbackFormat,w=d(e.missing)?e.missing:null,P=d(e.missing)?Ze(e.missing):null,C=d(e.postTranslation)?e.postTranslation:null,D=l?l.warnHtmlMessage:!E(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter;const S=l?l.modifiers:F(e.modifiers)?e.modifiers:{};let $,U=e.pluralRules||l&&l.pluralRules;$=(()=>{s&&me(null);const t={version:Pe,locale:g.value,fallbackLocale:k.value,messages:L.value,modifiers:S,pluralRules:U,missing:null===P?void 0:P,missingWarn:N,fallbackWarn:I,fallbackFormat:W,unresolving:!0,postTranslation:null===C?void 0:C,warnHtmlMessage:D,escapeParameter:A,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=T.value,t.numberFormats=y.value,t.__datetimeFormatters=F($)?$.__datetimeFormatters:void 0,t.__numberFormatters=F($)?$.__numberFormatters:void 0;const a=ge(t);return s&&me(a),a})(),de($,g.value,k.value);const x=t.computed({get:()=>g.value,set:e=>{g.value=e,$.locale=g.value}}),H=t.computed({get:()=>k.value,set:e=>{k.value=e,$.fallbackLocale=k.value,de($,g.value,e)}}),j=t.computed((()=>L.value)),V=t.computed((()=>T.value)),G=t.computed((()=>y.value));const Y=(e,t,a,n,r,i)=>{let c;g.value,k.value,L.value,T.value,y.value;try{0,s||($.fallbackContext=l?fe():void 0),c=e($)}finally{s||($.fallbackContext=void 0)}if("translate exists"!==a&&o(c)&&c===ne||"translate exists"===a&&!c){const[e,a]=t();return l&&O?n(l):r(e)}if(i(c))return c;throw Error(Ae.UNEXPECTED_RETURN_TYPE)};function B(...e){return Y((t=>Reflect.apply(ke,null,[t,...e])),(()=>Fe(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>b(e)))}const X={normalize:function(e){return e.map((e=>b(e)||o(e)||E(e)?Xe(String(e)):e))},interpolate:e=>e,type:"vnode"};function z(e){return L.value[e]||{}}qe++,l&&a&&(t.watch(l.locale,(e=>{_&&(g.value=e,$.locale=e,de($,g.value,k.value))})),t.watch(l.fallbackLocale,(e=>{_&&(k.value=e,$.fallbackLocale=e,de($,g.value,k.value))})));const J={id:qe,locale:x,fallbackLocale:H,get inheritLocale(){return _},set inheritLocale(e){_=e,e&&l&&(g.value=l.locale.value,k.value=l.fallbackLocale.value,de($,g.value,k.value))},get availableLocales(){return Object.keys(L.value).sort()},messages:j,get modifiers(){return S},get pluralRules(){return U||{}},get isGlobal(){return s},get missingWarn(){return N},set missingWarn(e){N=e,$.missingWarn=N},get fallbackWarn(){return I},set fallbackWarn(e){I=e,$.fallbackWarn=I},get fallbackRoot(){return O},set fallbackRoot(e){O=e},get fallbackFormat(){return W},set fallbackFormat(e){W=e,$.fallbackFormat=W},get warnHtmlMessage(){return D},set warnHtmlMessage(e){D=e,$.warnHtmlMessage=e},get escapeParameter(){return A},set escapeParameter(e){A=e,$.escapeParameter=e},t:B,getLocaleMessage:z,setLocaleMessage:function(e,t){if(c){const a={[e]:t};for(const e in a)p(a,e)&&Ve(a[e]);t=a[e]}L.value[e]=t,$.messages=L.value},mergeLocaleMessage:function(e,t){L.value[e]=L.value[e]||{};const a={[e]:t};if(c)for(const n in a)p(a,n)&&Ve(a[n]);R(t=a[e],L.value[e]),$.messages=L.value},getPostTranslationHandler:function(){return d(C)?C:null},setPostTranslationHandler:function(e){C=e,$.postTranslation=e},getMissingHandler:function(){return w},setMissingHandler:function(e){null!==e&&(P=Ze(e)),w=e,$.missing=P},[xe]:function(e){U=e,$.pluralRules=U}};return J.datetimeFormats=V,J.numberFormats=G,J.rt=function(...e){const[t,a,n]=e;if(n&&!h(n))throw Error(Ae.INVALID_ARGUMENT);return B(t,a,u({resolvedMessage:!0},n||{}))},J.te=function(e,t){return Y((()=>{if(!e)return!1;const a=z(b(t)?t:g.value),n=$.messageResolver(a,e);return f?null!=n:M(n)||he(n)||b(n)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),Je,(e=>E(e)))},J.tm=function(e){const t=function(e){let t=null;const a=K($,k.value,g.value);for(let n=0;n<a.length;n++){const l=L.value[a[n]]||{},r=$.messageResolver(l,e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:l&&l.tm(e)||{}},J.d=function(...e){return Y((t=>Reflect.apply(ye,null,[t,...e])),(()=>Ie(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>re),(e=>b(e)))},J.n=function(...e){return Y((t=>Reflect.apply(Oe,null,[t,...e])),(()=>We(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>re),(e=>b(e)))},J.getDateTimeFormat=function(e){return T.value[e]||{}},J.setDateTimeFormat=function(e,t){T.value[e]=t,$.datetimeFormats=T.value,Re($,e,t)},J.mergeDateTimeFormat=function(e,t){T.value[e]=u(T.value[e]||{},t),$.datetimeFormats=T.value,Re($,e,t)},J.getNumberFormat=function(e){return y.value[e]||{}},J.setNumberFormat=function(e,t){y.value[e]=t,$.numberFormats=y.value,we($,e,t)},J.mergeNumberFormat=function(e,t){y.value[e]=u(y.value[e]||{},t),$.numberFormats=y.value,we($,e,t)},J[He]=r,J[Se]=function(...e){return Y((t=>{let a;const n=t;try{n.processor=X,a=Reflect.apply(ke,null,[n,...e])}finally{n.processor=null}return a}),(()=>Fe(...e)),"translate",(t=>t[Se](...e)),(e=>[Xe(e)]),(e=>v(e)))},J[$e]=function(...e){return Y((t=>Reflect.apply(ye,null,[t,...e])),(()=>Ie(...e)),"datetime format",(t=>t[$e](...e)),ze,(e=>b(e)||v(e)))},J[Ue]=function(...e){return Y((t=>Reflect.apply(Oe,null,[t,...e])),(()=>We(...e)),"number format",(t=>t[Ue](...e)),ze,(e=>b(e)||v(e)))},J}function Qe(e={},t){{const t=Ke(function(e){const t=b(e.locale)?e.locale:le,a=b(e.fallbackLocale)||v(e.fallbackLocale)||F(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=d(e.missing)?e.missing:void 0,l=!E(e.silentTranslationWarn)&&!i(e.silentTranslationWarn)||!e.silentTranslationWarn,r=!E(e.silentFallbackWarn)&&!i(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!E(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=F(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,f=d(e.postTranslation)?e.postTranslation:void 0,_=!b(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,g=!!e.escapeParameterHtml,p=!E(e.sync)||e.sync;let h=e.messages;if(F(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return u(n,t[a]),e}),h||{})}const{__i18n:k,__root:L,__injectWithOption:T}=e,y=e.datetimeFormats,N=e.numberFormats,I=e.flatJson,R=e.translateExistCompatible;return{locale:t,fallbackLocale:a,messages:h,flatJson:I,datetimeFormats:y,numberFormats:N,missing:n,missingWarn:l,fallbackWarn:r,fallbackRoot:o,fallbackFormat:s,modifiers:c,pluralRules:m,postTranslation:f,warnHtmlMessage:_,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:p,translateExistCompatible:R,__i18n:k,__root:L,__injectWithOption:T}}(e)),{__extender:a}=e,n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return E(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=E(e)?!e:e},get silentFallbackWarn(){return E(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=E(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,l]=e,r={};let o=null,s=null;if(!b(a))throw Error(Ae.INVALID_ARGUMENT);const i=a;return b(n)?r.locale=n:v(n)?o=n:F(n)&&(s=n),v(l)?o=l:F(l)&&(s=l),Reflect.apply(t.t,t,[i,o||s||{},r])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,l]=e,r={plural:1};let s=null,i=null;if(!b(a))throw Error(Ae.INVALID_ARGUMENT);const c=a;return b(n)?r.locale=n:o(n)?r.plural=n:v(n)?s=n:F(n)&&(i=n),b(l)?r.locale=l:v(l)?s=l:F(l)&&(i=l),Reflect.apply(t.t,t,[c,s||i||{},r])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1};return n.__extender=a,n}}const et={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function tt(e){return t.Fragment}const at=t.defineComponent({name:"i18n-t",props:u({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},et),setup(e,a){const{slots:n,attrs:l}=a,r=e.i18n||gt({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter((e=>"_"!==e)),s=f();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=b(e.plural)?+e.plural:e.plural);const i=function({slots:e},a){if(1===a.length&&"default"===a[0])return(e.default?e.default():[]).reduce(((e,a)=>[...e,...a.type===t.Fragment?a.children:[a]]),[]);return a.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),f())}(a,o),c=r[Se](e.keypath,i,s),m=u(f(),l),_=b(e.tag)||h(e.tag)?e.tag:tt();return t.h(_,m,c)}}}),nt=at;function lt(e,a,n,l){const{slots:r,attrs:o}=a;return()=>{const a={part:!0};let s=f();e.locale&&(a.locale=e.locale),b(e.format)?a.key=e.format:h(e.format)&&(b(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?u(f(),t,{[a]:e.format[a]}):t),f()));const i=l(e.value,a,s);let c=[a.key];v(i)?c=i.map(((e,t)=>{const a=r[e.type],n=a?a({[e.type]:e.value,index:t,parts:i}):[e.value];var l;return v(l=n)&&!b(l[0])&&(n[0].key=`${e.type}-${t}`),n})):b(i)&&(c=[i]);const m=u(f(),o),_=b(e.tag)||h(e.tag)?e.tag:tt();return t.h(_,m,c)}}const rt=t.defineComponent({name:"i18n-n",props:u({value:{type:Number,required:!0},format:{type:[String,Object]}},et),setup(e,t){const a=e.i18n||gt({useScope:e.scope,__useComponent:!0});return lt(e,t,Me,((...e)=>a[Ue](...e)))}}),ot=rt,st=t.defineComponent({name:"i18n-d",props:u({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},et),setup(e,t){const a=e.i18n||gt({useScope:e.scope,__useComponent:!0});return lt(e,t,Ne,((...e)=>a[$e](...e)))}}),it=st;function ct(e){const n=t=>{const{instance:a,modifiers:n,value:l}=t;if(!a||!a.$)throw Error(Ae.UNEXPECTED_ERROR);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=ut(l);return[Reflect.apply(r.t,r,[...mt(o)]),r]};return{created:(l,r)=>{const[o,s]=n(r);a&&e.global===s&&(l.__i18nWatcher=t.watch(s.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),l.__composer=s,l.textContent=o},unmounted:e=>{a&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=ut(t);e.textContent=Reflect.apply(a.t,a,[...mt(n)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function ut(e){if(b(e))return{path:e};if(F(e)){if(!("path"in e))throw Error(Ae.REQUIRED_VALUE,"path");return e}throw Error(Ae.INVALID_VALUE)}function mt(e){const{path:t,locale:a,args:n,choice:l,plural:r}=e,s={},i=n||{};return b(a)&&(s.locale=a),o(l)&&(s.plural=l),o(r)&&(s.plural=r),[t,i,s]}function ft(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[xe](t.pluralizationRules||e.pluralizationRules);const a=Ge(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const _t=n("global-vue-i18n");function gt(e={}){const a=t.getCurrentInstance();if(null==a)throw Error(Ae.MUST_BE_CALL_SETUP_TOP);if(!a.isCE&&null!=a.appContext.app&&!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Ae.NOT_INSTALLED);const n=function(e){{const a=t.inject(e.isCE?_t:e.appContext.app.__VUE_I18N_SYMBOL__);if(!a)throw function(e,...t){return O(e,null,void 0)}(e.isCE?Ae.NOT_INSTALLED_WITH_PROVIDE:Ae.UNEXPECTED_ERROR);return a}}(a),l=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),r=Ye(a),o=function(e,t){return c(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,r);if("legacy"===n.mode&&!e.__useComponent){if(!n.allowComposition)throw Error(Ae.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,a,n,l={}){const r="local"===a,o=t.shallowRef(null);if(r&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(Ae.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=E(l.inheritLocale)?l.inheritLocale:!b(l.locale),c=t.ref(!r||s?n.locale.value:b(l.locale)?l.locale:le),u=t.ref(!r||s?n.fallbackLocale.value:b(l.fallbackLocale)||v(l.fallbackLocale)||F(l.fallbackLocale)||!1===l.fallbackLocale?l.fallbackLocale:c.value),m=t.ref(Ge(c.value,l)),f=t.ref(F(l.datetimeFormats)?l.datetimeFormats:{[c.value]:{}}),_=t.ref(F(l.numberFormats)?l.numberFormats:{[c.value]:{}}),g=r?n.missingWarn:!E(l.missingWarn)&&!i(l.missingWarn)||l.missingWarn,p=r?n.fallbackWarn:!E(l.fallbackWarn)&&!i(l.fallbackWarn)||l.fallbackWarn,h=r?n.fallbackRoot:!E(l.fallbackRoot)||l.fallbackRoot,k=!!l.fallbackFormat,L=d(l.missing)?l.missing:null,T=d(l.postTranslation)?l.postTranslation:null,y=r?n.warnHtmlMessage:!E(l.warnHtmlMessage)||l.warnHtmlMessage,N=!!l.escapeParameter,I=r?n.modifiers:F(l.modifiers)?l.modifiers:{},R=l.pluralRules||r&&n.pluralRules;function O(){return[c.value,u.value,m.value,f.value,_.value]}const M=t.computed({get:()=>o.value?o.value.locale.value:c.value,set:e=>{o.value&&(o.value.locale.value=e),c.value=e}}),W=t.computed({get:()=>o.value?o.value.fallbackLocale.value:u.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),u.value=e}}),w=t.computed((()=>o.value?o.value.messages.value:m.value)),P=t.computed((()=>f.value)),C=t.computed((()=>_.value));function D(){return o.value?o.value.getPostTranslationHandler():T}function A(e){o.value&&o.value.setPostTranslationHandler(e)}function S(){return o.value?o.value.getMissingHandler():L}function $(e){o.value&&o.value.setMissingHandler(e)}function U(e){return O(),e()}function x(...e){return o.value?U((()=>Reflect.apply(o.value.t,null,[...e]))):U((()=>""))}function H(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function j(...e){return o.value?U((()=>Reflect.apply(o.value.d,null,[...e]))):U((()=>""))}function V(...e){return o.value?U((()=>Reflect.apply(o.value.n,null,[...e]))):U((()=>""))}function G(e){return o.value?o.value.tm(e):{}}function Y(e,t){return!!o.value&&o.value.te(e,t)}function B(e){return o.value?o.value.getLocaleMessage(e):{}}function X(e,t){o.value&&(o.value.setLocaleMessage(e,t),m.value[e]=t)}function z(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function J(e){return o.value?o.value.getDateTimeFormat(e):{}}function q(e,t){o.value&&(o.value.setDateTimeFormat(e,t),f.value[e]=t)}function Z(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function K(e){return o.value?o.value.getNumberFormat(e):{}}function Q(e,t){o.value&&(o.value.setNumberFormat(e,t),_.value[e]=t)}function ee(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const te={get id(){return o.value?o.value.id:-1},locale:M,fallbackLocale:W,messages:w,datetimeFormats:P,numberFormats:C,get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(m.value)},get modifiers(){return o.value?o.value.modifiers:I},get pluralRules(){return o.value?o.value.pluralRules:R},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:g},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:p},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:h},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:k},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:y},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:N},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:x,getPostTranslationHandler:D,setPostTranslationHandler:A,getMissingHandler:S,setMissingHandler:$,rt:H,d:j,n:V,tm:G,te:Y,getLocaleMessage:B,setLocaleMessage:X,mergeLocaleMessage:z,getDateTimeFormat:J,setDateTimeFormat:q,mergeDateTimeFormat:Z,getNumberFormat:K,setNumberFormat:Q,mergeNumberFormat:ee};function ae(e){e.locale.value=c.value,e.fallbackLocale.value=u.value,Object.keys(m.value).forEach((t=>{e.mergeLocaleMessage(t,m.value[t])})),Object.keys(f.value).forEach((t=>{e.mergeDateTimeFormat(t,f.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=N,e.fallbackFormat=k,e.fallbackRoot=h,e.fallbackWarn=p,e.missingWarn=g,e.warnHtmlMessage=y}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(Ae.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===a?(c.value=t.locale.value,u.value=t.fallbackLocale.value,m.value=t.messages.value,f.value=t.datetimeFormats.value,_.value=t.numberFormats.value):r&&ae(t)})),te}(a,o,l,e)}if("global"===o)return Be(l,e,r),l;if("parent"===o){let t=function(e,t,a=!1){let n=null;const l=t.root;let r=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,a);for(;null!=r;){const t=e;if("composition"===e.mode)n=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(n=e.__composer,a&&n&&!n[He]&&(n=null))}if(null!=n)break;if(l===r)break;r=r.parent}return n}(n,a,e.__useComponent);return null==t&&(t=l),t}const s=n;let m=s.__getInstance(a);if(null==m){const n=u({},e);"__i18n"in r&&(n.__i18n=r.__i18n),l&&(n.__root=l),m=Ke(n),s.__composerExtend&&(m[je]=s.__composerExtend(m)),function(e,a,n){t.onMounted((()=>{}),a),t.onUnmounted((()=>{const t=n;e.__deleteInstance(a);const l=t[je];l&&(l(),delete t[je])}),a)}(s,a,m),s.__setInstance(a,m)}return m}const pt=["locale","fallbackLocale","availableLocales"],vt=["t","rt","d","n","tm","te"];return ie=function(e,t){if(!h(e))return null;let a=S.get(t);if(a||(a=function(e){const t=[];let a,n,l,r,o,s,i,c=-1,u=0,m=0;const f=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,l="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=l:n+=l},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=A(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!_()){if(r=D(a),i=P[u],o=i[r]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(l=a,!1===s())))return;if(7===u)return t}}(t),a&&S.set(t,a)),!a)return null;const n=a.length;let l=e,r=0;for(;r<n;){const e=a[r];if(w.includes(e)&&M(l))return null;const t=l[e];if(void 0===t)return null;if(d(l))return null;l=t,r++}return l},ce=K,e.DatetimeFormat=st,e.I18nD=it,e.I18nInjectionKey=_t,e.I18nN=ot,e.I18nT=nt,e.NumberFormat=rt,e.Translation=at,e.VERSION=Pe,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(Ae.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},a){const l=!E(e.legacy)||e.legacy,r=!E(e.globalInjection)||e.globalInjection,o=!l||!!e.allowComposition,s=new Map,[i,c]=function(e,a,n){const l=t.effectScope();{const t=a?l.run((()=>Qe(e))):l.run((()=>Ke(e)));if(null==t)throw Error(Ae.UNEXPECTED_ERROR);return[l,t]}}(e,l),u=n("");{const e={get mode(){return l?"legacy":"composition"},get allowComposition(){return o},async install(a,...n){if(a.__VUE_I18N_SYMBOL__=u,a.provide(a.__VUE_I18N_SYMBOL__,e),F(n[0])){const t=n[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let o=null;!l&&r&&(o=function(e,a){const n=Object.create(null);pt.forEach((e=>{const l=Object.getOwnPropertyDescriptor(a,e);if(!l)throw Error(Ae.UNEXPECTED_ERROR);const r=t.isRef(l.value)?{get:()=>l.value.value,set(e){l.value.value=e}}:{get:()=>l.get&&l.get()};Object.defineProperty(n,e,r)})),e.config.globalProperties.$i18n=n,vt.forEach((t=>{const n=Object.getOwnPropertyDescriptor(a,t);if(!n||!n.value)throw Error(Ae.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,n)}));const l=()=>{delete e.config.globalProperties.$i18n,vt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return l}(a,e.global)),function(e,t,...a){const n=F(a[0])?a[0]:{},l=!!n.useI18nComponentName;(!E(n.globalInstall)||n.globalInstall)&&([l?"i18n":at.name,"I18nT"].forEach((t=>e.component(t,at))),[rt.name,"I18nN"].forEach((t=>e.component(t,rt))),[st.name,"I18nD"].forEach((t=>e.component(t,st)))),e.directive("t",ct(t))}(a,e,...n),l&&a.mixin(function(e,a,n){return{beforeCreate(){const l=t.getCurrentInstance();if(!l)throw Error(Ae.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const t=r.i18n;if(r.__i18n&&(t.__i18n=r.__i18n),t.__root=a,this===this.$root)this.$i18n=ft(e,t);else{t.__injectWithOption=!0,t.__extender=n.__vueI18nExtend,this.$i18n=Qe(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=ft(e,r);else{this.$i18n=Qe({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:a});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&Be(a,r,r),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(l,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(Ae.UNEXPECTED_ERROR);const a=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,a.__disposer&&(a.__disposer(),delete a.__disposer,delete a.__extender),n.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,e));const s=a.unmount;a.unmount=()=>{o&&o(),e.dispose(),s()}},get global(){return c},dispose(){i.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=gt,e.vTDirective=ct,e}({},Vue);
