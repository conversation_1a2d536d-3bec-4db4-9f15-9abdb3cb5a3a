<template>
  <section class="cta-section section">
    <div class="container">
      <div class="cta-content">
        <h2 class="cta-title">
          {{ $t('cta.title') }}
        </h2>
        <p class="cta-subtitle">
          {{ $t('cta.subtitle') }}
        </p>

        <div class="cta-actions">
          <RouterLink to="/contact" class="btn btn-primary cta-primary">
            <span>{{ $t('cta.button') }}</span>
            <svg class="btn-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </RouterLink>

          <RouterLink to="/solutions" class="btn btn-outline cta-secondary">
            <span>{{ $t('cta.contact') }}</span>
          </RouterLink>
        </div>
        
        <div class="cta-features">
          <div class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Free Consultation</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">✓</span>
            <span>30-Day Trial</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">✓</span>
            <span>24/7 Support</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>

<style scoped lang="scss">
.cta-section {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  
  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
  
  @media (max-width: $breakpoint-sm) {
    font-size: 2rem;
  }
}

.cta-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  
  @media (max-width: $breakpoint-md) {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
}

.cta-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: center;
  }
}

.cta-primary, .cta-secondary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  
  .btn-icon {
    transition: transform 0.3s ease;
  }
  
  &:hover .btn-icon {
    transform: translateX(3px);
  }
}

.cta-features {
  display: flex;
  gap: 2rem;
  justify-content: center;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    gap: 1rem;
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: $color-text-light;
  
  .feature-icon {
    color: $color-neon-green;
    font-weight: bold;
    font-size: 1.2rem;
  }
}
</style>
