<template>
  <section class="hero">
    <div class="hero__background">
      <canvas ref="heroCanvasRef" class="hero__canvas"></canvas>
    </div>
    
    <div class="container hero__container">
      <div class="hero__content">
        <div class="hero__badge" :class="{ 'animate-fade-in': isVisible }">
          <span class="badge-text">Next Generation IoT</span>
        </div>
        
        <h1 class="hero__title" :class="{ 'animate-slide-in-up delay-200': isVisible }">
          Welcome to the
          <span class="title-highlight">Future</span>
          of Connected Intelligence
        </h1>
        
        <p class="hero__subtitle" :class="{ 'animate-slide-in-up delay-300': isVisible }">
          Transform your world with cutting-edge IoT solutions that bridge the gap between 
          imagination and reality. Where every device becomes intelligent, every connection 
          meaningful, and every data point a step toward tomorrow.
        </p>
        
        <div class="hero__actions" :class="{ 'animate-slide-in-up delay-500': isVisible }">
          <RouterLink to="/contact" class="btn btn-primary hero__cta">
            <span>Explore Solutions</span>
            <svg class="btn-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </RouterLink>
          
          <button class="btn btn-ghost hero__demo" @click="playDemo">
            <svg class="btn-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <polygon points="5,3 19,12 5,21" fill="currentColor"/>
            </svg>
            <span>Watch Demo</span>
          </button>
        </div>
        
        <div class="hero__stats" :class="{ 'animate-fade-in delay-700': isVisible }">
          <div class="stat">
            <div class="stat-number">500+</div>
            <div class="stat-label">Projects Delivered</div>
          </div>
          <div class="stat">
            <div class="stat-number">50M+</div>
            <div class="stat-label">Devices Connected</div>
          </div>
          <div class="stat">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">Uptime Guaranteed</div>
          </div>
        </div>
      </div>
      
      <div class="hero__visual" :class="{ 'animate-scale-in delay-400': isVisible }">
        <div class="visual-container">
          <div class="floating-elements">
            <div class="element element--1" :class="{ 'animate-float': isVisible }"></div>
            <div class="element element--2" :class="{ 'animate-float delay-100': isVisible }"></div>
            <div class="element element--3" :class="{ 'animate-float delay-200': isVisible }"></div>
          </div>
          <div class="central-hub">
            <div class="hub-core" :class="{ 'animate-pulse-neon': isVisible }"></div>
            <div class="hub-rings">
              <div class="ring ring--1"></div>
              <div class="ring ring--2"></div>
              <div class="ring ring--3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="hero__scroll-indicator" :class="{ 'animate-fade-in delay-1000': isVisible }">
      <div class="scroll-text">Scroll to explore</div>
      <div class="scroll-arrow"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { RouterLink } from 'vue-router'

const heroCanvasRef = ref<HTMLCanvasElement | null>(null)
const isVisible = ref(false)

let animationId: number | null = null
let canvas: HTMLCanvasElement
let ctx: CanvasRenderingContext2D

const playDemo = () => {
  // Demo functionality - could open a modal or navigate to demo page
  console.log('Playing demo...')
}

const initHeroAnimation = () => {
  if (!heroCanvasRef.value) return
  
  canvas = heroCanvasRef.value
  ctx = canvas.getContext('2d')!
  
  const resizeCanvas = () => {
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight
  }
  
  resizeCanvas()
  window.addEventListener('resize', resizeCanvas)
  
  // Simple data flow animation
  const dataPoints: Array<{x: number, y: number, vx: number, vy: number, opacity: number}> = []
  
  const createDataPoint = () => {
    return {
      x: -20,
      y: Math.random() * canvas.height,
      vx: 1 + Math.random() * 2,
      vy: (Math.random() - 0.5) * 0.5,
      opacity: 0.3 + Math.random() * 0.4
    }
  }
  
  // Initialize data points
  for (let i = 0; i < 5; i++) {
    dataPoints.push(createDataPoint())
  }
  
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // Update and draw data points
    dataPoints.forEach((point, index) => {
      point.x += point.vx
      point.y += point.vy
      
      // Reset if off screen
      if (point.x > canvas.width + 20) {
        dataPoints[index] = createDataPoint()
        return
      }
      
      // Draw data point
      ctx.save()
      ctx.globalAlpha = point.opacity
      ctx.fillStyle = '#00FFFF'
      ctx.shadowBlur = 10
      ctx.shadowColor = '#00FFFF'
      
      ctx.beginPath()
      ctx.arc(point.x, point.y, 2, 0, Math.PI * 2)
      ctx.fill()
      
      // Draw trail
      ctx.strokeStyle = '#00FFFF'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(point.x - 20, point.y)
      ctx.lineTo(point.x, point.y)
      ctx.stroke()
      
      ctx.restore()
    })
    
    animationId = requestAnimationFrame(animate)
  }
  
  animate()
}

onMounted(() => {
  // Trigger animations after mount
  setTimeout(() => {
    isVisible.value = true
  }, 100)
  
  // Initialize hero canvas animation
  setTimeout(() => {
    initHeroAnimation()
  }, 500)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped lang="scss">
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero__canvas {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.hero__container {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;

  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

.hero__content {
  max-width: 600px;

  @media (max-width: $breakpoint-lg) {
    max-width: none;
    order: 2;
  }
}

.hero__badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: $border-radius-full;
  margin-bottom: 2rem;
  
  .badge-text {
    color: $color-neon-aqua;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.hero__title {
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  
  .title-highlight {
    background: $gradient-neon;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      right: 0;
      height: 3px;
      background: $gradient-neon;
      border-radius: 2px;
      opacity: 0.7;
    }
  }

  @media (max-width: $breakpoint-lg) {
    font-size: 2.5rem;
  }

  @media (max-width: $breakpoint-md) {
    font-size: 2rem;
  }
}

.hero__subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: $color-text-muted;
  margin-bottom: 2.5rem;
  max-width: 500px;

  @media (max-width: $breakpoint-lg) {
    max-width: none;
  }
}

.hero__actions {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: center;
  }
}

.hero__cta, .hero__demo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .btn-icon {
    transition: transform 0.3s ease;
  }
  
  &:hover .btn-icon {
    transform: translateX(3px);
  }
}

.hero__stats {
  display: flex;
  gap: 3rem;

  @media (max-width: $breakpoint-md) {
    justify-content: center;
    gap: 2rem;
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

.stat {
  text-align: center;
  
  .stat-number {
    font-family: $font-heading;
    font-size: 2rem;
    font-weight: 700;
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
    margin-bottom: 0.5rem;
  }
  
  .stat-label {
    font-size: 0.9rem;
    color: $color-text-muted;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.hero__visual {
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: $breakpoint-lg) {
    order: 1;
  }
}

.visual-container {
  position: relative;
  width: 400px;
  height: 400px;

  @media (max-width: $breakpoint-md) {
    width: 300px;
    height: 300px;
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(0, 255, 255, 0.2);
  border: 2px solid $color-neon-aqua;
  border-radius: 50%;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: $color-neon-aqua;
    border-radius: 50%;
    box-shadow: 0 0 20px currentColor;
  }
  
  &--1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
  }
  
  &--2 {
    bottom: 30%;
    left: 5%;
    animation-delay: 1s;
  }
  
  &--3 {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
  }
}

.central-hub {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.hub-core {
  width: 80px;
  height: 80px;
  background: $gradient-neon;
  border-radius: 50%;
  position: relative;
  z-index: 2;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: $color-primary-bg;
    border-radius: 50%;
  }
}

.hub-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  animation: rotate-360 10s linear infinite;
  
  &--1 {
    width: 120px;
    height: 120px;
    top: -60px;
    left: -60px;
  }
  
  &--2 {
    width: 180px;
    height: 180px;
    top: -90px;
    left: -90px;
    animation-duration: 15s;
    animation-direction: reverse;
  }
  
  &--3 {
    width: 240px;
    height: 240px;
    top: -120px;
    left: -120px;
    animation-duration: 20s;
  }
}

.hero__scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: $color-text-muted;
  font-size: 0.9rem;
  
  .scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    transform: rotate(45deg);
    animation: float 2s ease-in-out infinite;
  }
}
</style>
