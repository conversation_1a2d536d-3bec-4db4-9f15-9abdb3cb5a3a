<template>
  <section class="features section">
    <div class="container">
      <div class="features__header">
        <h2 class="features__title">
          Why Choose <span class="text-gradient">IoTVision</span>
        </h2>
        <p class="features__subtitle">
          Experience the next generation of IoT solutions designed for the future
        </p>
      </div>
      
      <div class="features__grid">
        <div
          v-for="(feature, index) in features"
          :key="feature.id"
          class="feature-card"
          :class="{ 'animate-scale-in': isVisible }"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="feature-card__icon">
            <div class="icon-placeholder">{{ feature.iconText }}</div>
          </div>
          <h3 class="feature-card__title">{{ feature.title }}</h3>
          <p class="feature-card__description">{{ feature.description }}</p>
          <div class="feature-card__stats">
            <div class="stat">
              <span class="stat-value">{{ feature.stat.value }}</span>
              <span class="stat-label">{{ feature.stat.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const isVisible = ref(false)

const features = [
  {
    id: 1,
    title: 'Ultra-Fast Connectivity',
    description: 'Lightning-fast data transmission with 99.9% uptime guarantee. Our advanced protocols ensure your devices stay connected.',
    iconText: '⚡',
    stat: {
      value: '<1ms',
      label: 'Latency'
    }
  },
  {
    id: 2,
    title: 'AI-Powered Analytics',
    description: 'Transform raw data into actionable insights with our machine learning algorithms and predictive analytics.',
    iconText: '🧠',
    stat: {
      value: '95%',
      label: 'Accuracy'
    }
  },
  {
    id: 3,
    title: 'Enterprise Security',
    description: 'Bank-grade encryption and multi-layer security protocols protect your data and devices from threats.',
    iconText: '🛡️',
    stat: {
      value: '256-bit',
      label: 'Encryption'
    }
  },
  {
    id: 4,
    title: 'Scalable Infrastructure',
    description: 'From prototype to production, our platform scales seamlessly to support millions of connected devices.',
    iconText: '📈',
    stat: {
      value: '10M+',
      label: 'Devices'
    }
  },
  {
    id: 5,
    title: 'Real-time Monitoring',
    description: 'Monitor your entire IoT ecosystem in real-time with our intuitive dashboard and alert system.',
    iconText: '📊',
    stat: {
      value: '24/7',
      label: 'Monitoring'
    }
  },
  {
    id: 6,
    title: 'Edge Computing',
    description: 'Process data at the edge for reduced latency and improved performance with our distributed computing.',
    iconText: '🔗',
    stat: {
      value: '50%',
      label: 'Faster'
    }
  }
]

onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isVisible.value = true
        }
      })
    },
    { threshold: 0.1 }
  )
  
  const section = document.querySelector('.features')
  if (section) {
    observer.observe(section)
  }
})
</script>

<style scoped lang="scss">
.features {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.5) 0%, rgba(10, 10, 15, 0.8) 100%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }
}

.features__header {
  text-align: center;
  margin-bottom: 4rem;
  
  .features__title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    
    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
    }
  }
  
  .features__subtitle {
    font-size: 1.2rem;
    color: $color-text-muted;
    max-width: 600px;
    margin: 0 auto;
  }
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  
  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.feature-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }
  
  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    
    &::before {
      left: 100%;
    }
  }
}

.feature-card__icon {
  width: 60px;
  height: 60px;
  background: $gradient-neon;
  border-radius: $border-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: $color-primary-bg;
    border-radius: calc($border-radius-md - 2px);
  }
  
  .icon-placeholder {
    font-size: 30px;
    position: relative;
    z-index: 1;
  }
}

.feature-card__title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: $color-text-white;
}

.feature-card__description {
  color: $color-text-muted;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-card__stats {
  .stat {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    
    .stat-value {
      font-family: $font-heading;
      font-size: 1.5rem;
      font-weight: 700;
      color: $color-neon-green;
      text-shadow: 0 0 10px currentColor;
    }
    
    .stat-label {
      font-size: 0.9rem;
      color: $color-text-muted;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

</style>
