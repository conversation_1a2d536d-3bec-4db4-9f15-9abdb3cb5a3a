<template>
  <div class="particle-background">
    <canvas ref="canvasRef" class="particle-canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const canvasRef = ref<HTMLCanvasElement | null>(null)
let animationId: number | null = null
let particles: Particle[] = []

interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  size: number
  opacity: number
  color: string
  connections: number[]
}

const colors = ['#00FFFF', '#00FF7F', '#FF00FF']
const maxParticles = 80
const connectionDistance = 150
const mouseInfluence = 100

let mouse = { x: 0, y: 0 }
let canvas: HTMLCanvasElement
let ctx: CanvasRenderingContext2D

const createParticle = (index: number): Particle => {
  return {
    x: Math.random() * canvas.width,
    y: Math.random() * canvas.height,
    vx: (Math.random() - 0.5) * 0.5,
    vy: (Math.random() - 0.5) * 0.5,
    size: Math.random() * 2 + 1,
    opacity: Math.random() * 0.5 + 0.3,
    color: colors[Math.floor(Math.random() * colors.length)],
    connections: []
  }
}

const initParticles = () => {
  particles = []
  for (let i = 0; i < maxParticles; i++) {
    particles.push(createParticle(i))
  }
}

const updateParticle = (particle: Particle) => {
  // Update position
  particle.x += particle.vx
  particle.y += particle.vy

  // Mouse interaction
  const dx = mouse.x - particle.x
  const dy = mouse.y - particle.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance < mouseInfluence) {
    const force = (mouseInfluence - distance) / mouseInfluence
    particle.vx += (dx / distance) * force * 0.01
    particle.vy += (dy / distance) * force * 0.01
  }

  // Boundary collision
  if (particle.x < 0 || particle.x > canvas.width) {
    particle.vx *= -1
    particle.x = Math.max(0, Math.min(canvas.width, particle.x))
  }
  if (particle.y < 0 || particle.y > canvas.height) {
    particle.vy *= -1
    particle.y = Math.max(0, Math.min(canvas.height, particle.y))
  }

  // Damping
  particle.vx *= 0.99
  particle.vy *= 0.99

  // Opacity pulsing
  particle.opacity += (Math.random() - 0.5) * 0.01
  particle.opacity = Math.max(0.1, Math.min(0.8, particle.opacity))
}

const drawParticle = (particle: Particle) => {
  ctx.save()
  ctx.globalAlpha = particle.opacity
  ctx.fillStyle = particle.color
  ctx.shadowBlur = 10
  ctx.shadowColor = particle.color
  
  ctx.beginPath()
  ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
  ctx.fill()
  
  ctx.restore()
}

const drawConnections = () => {
  for (let i = 0; i < particles.length; i++) {
    for (let j = i + 1; j < particles.length; j++) {
      const dx = particles[i].x - particles[j].x
      const dy = particles[i].y - particles[j].y
      const distance = Math.sqrt(dx * dx + dy * dy)

      if (distance < connectionDistance) {
        const opacity = (1 - distance / connectionDistance) * 0.3
        
        ctx.save()
        ctx.globalAlpha = opacity
        ctx.strokeStyle = particles[i].color
        ctx.lineWidth = 0.5
        ctx.shadowBlur = 5
        ctx.shadowColor = particles[i].color
        
        ctx.beginPath()
        ctx.moveTo(particles[i].x, particles[i].y)
        ctx.lineTo(particles[j].x, particles[j].y)
        ctx.stroke()
        
        ctx.restore()
      }
    }
  }
}

const animate = () => {
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Update and draw particles
  particles.forEach(particle => {
    updateParticle(particle)
    drawParticle(particle)
  })

  // Draw connections
  drawConnections()

  animationId = requestAnimationFrame(animate)
}

const resizeCanvas = () => {
  if (!canvas) return
  
  canvas.width = window.innerWidth
  canvas.height = window.innerHeight
  
  // Reinitialize particles with new canvas size
  initParticles()
}

const handleMouseMove = (event: MouseEvent) => {
  mouse.x = event.clientX
  mouse.y = event.clientY
}

const handleResize = () => {
  resizeCanvas()
}

onMounted(() => {
  canvas = canvasRef.value!
  ctx = canvas.getContext('2d')!
  
  resizeCanvas()
  initParticles()
  animate()
  
  window.addEventListener('resize', handleResize)
  window.addEventListener('mousemove', handleMouseMove)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('mousemove', handleMouseMove)
})
</script>

<style scoped lang="scss">
.particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.particle-canvas {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
