<template>
  <header class="header" :class="{ 'header--scrolled': isScrolled }">
    <nav class="nav container">
      <!-- Logo -->
      <div class="nav__logo">
        <RouterLink to="/" class="logo-link">
          <span class="logo-text">IoT</span>
          <span class="logo-accent">Vision</span>
        </RouterLink>
      </div>

      <!-- Desktop Navigation -->
      <ul class="nav__menu" :class="{ 'nav__menu--open': isMobileMenuOpen }">
        <li class="nav__item">
          <RouterLink to="/" class="nav__link" @click="closeMobileMenu">
            Home
          </RouterLink>
        </li>
        <li class="nav__item">
          <RouterLink to="/solutions" class="nav__link" @click="closeMobileMenu">
            Solutions
          </RouterLink>
        </li>
        <li class="nav__item">
          <RouterLink to="/technologies" class="nav__link" @click="closeMobileMenu">
            Technologies
          </RouterLink>
        </li>
        <li class="nav__item">
          <RouterLink to="/about" class="nav__link" @click="closeMobileMenu">
            About
          </RouterLink>
        </li>
        <li class="nav__item">
          <RouterLink to="/contact" class="nav__link" @click="closeMobileMenu">
            Contact
          </RouterLink>
        </li>
      </ul>

      <!-- CTA Button -->
      <div class="nav__cta">
        <RouterLink to="/contact" class="btn btn-primary">
          Get Started
        </RouterLink>
      </div>

      <!-- Mobile Menu Toggle -->
      <button 
        class="nav__toggle"
        @click="toggleMobileMenu"
        :aria-expanded="isMobileMenuOpen"
        aria-label="Toggle navigation menu"
      >
        <span class="nav__toggle-line"></span>
        <span class="nav__toggle-line"></span>
        <span class="nav__toggle-line"></span>
      </button>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { RouterLink } from 'vue-router'

const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 15, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  transition: all 0.3s ease;

  &--scrolled {
    background: rgba(10, 10, 15, 0.95);
    border-bottom-color: rgba(0, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  }
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  min-height: 70px;
}

.nav__logo {
  z-index: 1001;
}

.logo-link {
  display: flex;
  align-items: center;
  font-family: $font-heading;
  font-size: 1.5rem;
  font-weight: 900;
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.logo-text {
  color: $color-text-white;
  margin-right: 0.2rem;
}

.logo-accent {
  background: $gradient-text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav__menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;

  @media (max-width: $breakpoint-lg) {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: right 0.3s ease;
    border-left: 1px solid rgba(0, 255, 255, 0.2);

    &--open {
      right: 0;
    }
  }
}

.nav__item {
  position: relative;
}

.nav__link {
  color: $color-text-light;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: $border-radius-md;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
  }

  &.router-link-active {
    color: $color-neon-aqua;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 2px;
      background: $gradient-neon;
      border-radius: 1px;
    }
  }

  @media (max-width: $breakpoint-lg) {
    font-size: 1.2rem;
    padding: 1rem 2rem;
  }
}

.nav__cta {
  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}

.nav__toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 1001;

  @media (max-width: $breakpoint-lg) {
    display: flex;
  }
}

.nav__toggle-line {
  width: 100%;
  height: 3px;
  background: $color-neon-aqua;
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;

  .nav__menu--open ~ .nav__toggle & {
    &:nth-child(1) {
      transform: rotate(45deg) translate(7px, 7px);
    }

    &:nth-child(2) {
      opacity: 0;
    }

    &:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -7px);
    }
  }
}
</style>
