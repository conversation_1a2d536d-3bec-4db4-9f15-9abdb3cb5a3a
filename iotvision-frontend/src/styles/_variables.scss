// IoTVision Design System Variables

// Color Palette - Dark Futuristic Theme
$color-primary-bg: #0A0A0F;
$color-dark-blue: #1A1A2E;
$color-anthracite: #2C2C3E;

// Neon Accent Colors
$color-neon-aqua: #00FFFF;
$color-neon-green: #00FF7F;
$color-neon-fuchsia: #FF00FF;

// Text Colors
$color-text-light: #E0E0E0;
$color-text-white: #FFFFFF;
$color-text-muted: #A0A0A0;

// Typography
$font-heading: 'Orbitron', sans-serif;
$font-subheading: 'Inter', sans-serif;
$font-body: 'Open Sans', sans-serif;
$font-code: 'Fira Code', monospace;

// Font Sizes
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;
$font-size-4xl: 2.25rem;
$font-size-5xl: 3rem;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-2xl: 3rem;
$spacing-3xl: 4rem;

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index layers
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Animation durations
$duration-fast: 0.15s;
$duration-normal: 0.3s;
$duration-slow: 0.5s;

// Border radius
$border-radius-sm: 0.25rem;
$border-radius-md: 0.5rem;
$border-radius-lg: 1rem;
$border-radius-full: 9999px;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 255, 255, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 255, 255, 0.1), 0 2px 4px -1px rgba(0, 255, 255, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 255, 255, 0.1), 0 4px 6px -2px rgba(0, 255, 255, 0.05);
$shadow-neon: 0 0 20px rgba(0, 255, 255, 0.3);
$shadow-neon-green: 0 0 20px rgba(0, 255, 127, 0.3);
$shadow-neon-fuchsia: 0 0 20px rgba(255, 0, 255, 0.3);

// Gradients
$gradient-primary: linear-gradient(135deg, $color-dark-blue 0%, $color-anthracite 100%);
$gradient-neon: linear-gradient(135deg, $color-neon-aqua 0%, $color-neon-green 50%, $color-neon-fuchsia 100%);
$gradient-text: linear-gradient(135deg, $color-neon-aqua 0%, $color-neon-green 100%);
