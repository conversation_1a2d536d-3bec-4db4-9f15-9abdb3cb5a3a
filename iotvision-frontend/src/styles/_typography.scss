// Typography styles

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500&display=swap');

// Heading styles
h1, h2, h3, h4, h5, h6 {
  font-family: $font-heading;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: $spacing-md;
  background: $gradient-text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h1 {
  font-size: $font-size-5xl;
  font-weight: 900;
  letter-spacing: -0.02em;
  
  @media (max-width: $breakpoint-md) {
    font-size: $font-size-4xl;
  }
}

h2 {
  font-size: $font-size-4xl;
  font-weight: 700;
  
  @media (max-width: $breakpoint-md) {
    font-size: $font-size-3xl;
  }
}

h3 {
  font-size: $font-size-3xl;
  font-weight: 700;
  
  @media (max-width: $breakpoint-md) {
    font-size: $font-size-2xl;
  }
}

h4 {
  font-size: $font-size-2xl;
  font-weight: 600;
}

h5 {
  font-size: $font-size-xl;
  font-weight: 600;
}

h6 {
  font-size: $font-size-lg;
  font-weight: 600;
}

// Paragraph styles
p {
  margin-bottom: $spacing-md;
  color: $color-text-light;
  
  &.lead {
    font-size: $font-size-lg;
    font-weight: 300;
    color: $color-text-white;
  }
  
  &.small {
    font-size: $font-size-sm;
    color: $color-text-muted;
  }
}

// Link styles
a {
  color: $color-neon-aqua;
  transition: color $duration-fast ease;
  
  &:hover {
    color: $color-neon-green;
    text-shadow: 0 0 8px currentColor;
  }
  
  &:focus {
    outline: 2px solid $color-neon-aqua;
    outline-offset: 2px;
  }
}

// Code styles
code {
  font-family: $font-code;
  font-size: 0.9em;
  background: rgba(0, 255, 255, 0.1);
  padding: 0.2em 0.4em;
  border-radius: $border-radius-sm;
  color: $color-neon-aqua;
}

pre {
  font-family: $font-code;
  background: $color-dark-blue;
  padding: $spacing-lg;
  border-radius: $border-radius-md;
  overflow-x: auto;
  border: 1px solid rgba(0, 255, 255, 0.2);
  
  code {
    background: none;
    padding: 0;
  }
}

// Utility classes
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-neon {
  color: $color-neon-aqua;
  text-shadow: 0 0 10px currentColor;
}

.text-neon-green {
  color: $color-neon-green;
  text-shadow: 0 0 10px currentColor;
}

.text-neon-fuchsia {
  color: $color-neon-fuchsia;
  text-shadow: 0 0 10px currentColor;
}

.text-gradient {
  background: $gradient-text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.font-heading {
  font-family: $font-heading;
}

.font-body {
  font-family: $font-body;
}

.font-code {
  font-family: $font-code;
}
