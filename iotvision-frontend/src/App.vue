<script setup lang="ts">
import { RouterView } from 'vue-router'
import AppHeader from './components/layout/AppHeader.vue'
import AppFooter from './components/layout/AppFooter.vue'
import ParticleBackground from './components/effects/ParticleBackground.vue'
</script>

<template>
  <div id="app" class="app">
    <!-- Particle background effect -->
    <ParticleBackground />

    <!-- Main application structure -->
    <AppHeader />

    <main class="main-content">
      <RouterView />
    </main>

    <AppFooter />
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 10;
}
</style>
