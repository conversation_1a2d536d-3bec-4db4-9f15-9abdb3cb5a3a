<script setup lang="ts">
import HeroSection from '@/components/sections/HeroSection.vue'
import FeaturesSection from '@/components/sections/FeaturesSection.vue'
import TechnologiesPreview from '@/components/sections/TechnologiesPreview.vue'
import CTASection from '@/components/sections/CTASection.vue'
</script>

<template>
  <div class="home-view">
    <HeroSection />
    <FeaturesSection />
    <TechnologiesPreview />
    <CTASection />
  </div>
</template>

<style scoped>
.home-view {
  padding-top: 70px; /* Account for fixed header */
}
</style>
