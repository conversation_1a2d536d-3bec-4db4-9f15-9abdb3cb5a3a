version: '3.8'

services:
  # Frontend service
  frontend:
    build:
      context: ./iotvision-frontend
      dockerfile: Dockerfile
    container_name: iotvision-frontend
    ports:
      - "80:80"
    restart: unless-stopped
    networks:
      - iotvision-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`localhost`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"

  # Reverse proxy (optional for production)
  traefik:
    image: traefik:v2.10
    container_name: iotvision-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "8080:8080"  # Traefik dashboard
      - "443:443"    # HTTPS
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - iotvision-network
    restart: unless-stopped
    profiles:
      - production

networks:
  iotvision-network:
    driver: bridge
    name: iotvision-network

volumes:
  iotvision-data:
    name: iotvision-data
